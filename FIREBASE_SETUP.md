# Firebase Setup Instructions for Aifrobeats.com 2.0

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Name your project (e.g., "aifrobeats-com")
4. Enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Add Web App

1. In your Firebase project, click the web icon (</>) to add a web app
2. Register app with nickname "Aifrobeats Web App"
3. Check "Also set up Firebase Hosting for this app"
4. Click "Register app"
5. **IMPORTANT**: Copy the Firebase configuration object - you'll need this!

## Step 3: Configure Authentication

1. In Firebase Console, go to "Authentication" > "Sign-in method"
2. Click on "Email/Password"
3. Enable "Email/Password" provider
4. Click "Save"

## Step 4: Create Firestore Database

1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in production mode"
4. Select your preferred location
5. Click "Done"

## Step 5: Set Up Security Rules

1. In Firestore Database, go to "Rules" tab
2. Replace the default rules with the content from `firestore.rules`
3. Click "Publish"

## Step 6: Update Configuration Files

### Update Firebase Config in All HTML Files

Replace the placeholder Firebase configuration in these files:
- `login.html` (line ~125)
- `signup.html` (line ~125)  
- `dashboard.html` (line ~175)
- `request-form.html` (line ~200)
- `index.html` (line ~1070)

Replace this placeholder:
```javascript
const firebaseConfig = {
    apiKey: "YOUR_API_KEY_HERE",
    authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_PROJECT_ID.appspot.com",
    messagingSenderId: "YOUR_SENDER_ID",
    appId: "YOUR_APP_ID"
};
```

With your actual Firebase configuration from Step 2.

### Update firebase-config.js (Optional - for future use)

The `firebase-config.js` file is prepared for modular Firebase SDK usage. Update the configuration there as well if you plan to use it.

## Step 7: Test the Setup

1. Open `index.html` in your browser
2. Click "Sign Up Free" 
3. Create a test account
4. Verify you're redirected to the dashboard
5. Check that you have 3 credits
6. Try creating a song request

## Step 8: Deploy to Firebase Hosting (Optional)

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Initialize hosting: `firebase init hosting`
   - Select your Firebase project
   - Set public directory to current directory (.)
   - Configure as single-page app: No
   - Set up automatic builds: No
4. Deploy: `firebase deploy`

## Current Features Implemented

✅ **Authentication**
- Email/password signup and login
- Firebase UI Auth integration
- Automatic user document creation with 3 free credits
- Auth state management across all pages

✅ **User Dashboard**
- Display user credits
- List user's song requests
- Real-time updates using Firestore listeners

✅ **Song Request System**
- Comprehensive request form
- Credit validation before submission
- Real-time credit deduction
- Request status tracking

✅ **Navigation Updates**
- Dynamic login/logout buttons
- User credit display in navigation
- Mobile-responsive auth menu

✅ **Security**
- Firestore security rules
- User data isolation
- Protected routes

## Next Steps (Day 1 Afternoon - Day 2)

1. **Admin Dashboard** - Create admin interface for managing requests
2. **File Upload** - Add Firebase Storage for audio file uploads
3. **Payment Integration** - Stripe integration for credit purchases
4. **Email Notifications** - Notify users when songs are ready
5. **Enhanced UI** - Improve styling and user experience

## Troubleshooting

### Common Issues:

1. **"Firebase not defined" error**
   - Make sure Firebase scripts are loaded before your custom scripts
   - Check that Firebase configuration is correct

2. **Permission denied errors**
   - Verify Firestore security rules are published
   - Check that user is authenticated

3. **Credits not updating**
   - Check browser console for errors
   - Verify Firestore rules allow user document updates

4. **Redirect loops**
   - Clear browser cache and cookies
   - Check auth state listeners aren't conflicting

### Debug Tips:

- Open browser Developer Tools (F12)
- Check Console tab for JavaScript errors
- Check Network tab for failed requests
- Use Firebase Console to view database contents

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify all Firebase configuration values are correct
3. Test with a fresh browser session (incognito mode)
4. Check Firebase Console for any service issues
