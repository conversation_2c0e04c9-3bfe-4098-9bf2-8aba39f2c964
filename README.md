# Aifrobeats Form with EmailJS Integration

This README provides instructions on how to set up the EmailJS integration for the Aifrobeats order form.

## Overview

The form is set up to:
1. Collect user information
2. Send the information to your email via EmailJS
3. Redirect the user to the Gumroad product page after successful submission

## Setting Up EmailJS

### Step 1: Create an EmailJS Account
1. Go to [EmailJS](https://www.emailjs.com/) and sign up for an account
2. After signing up, you'll get a public key that you'll need to add to the code

### Step 2: Create an Email Service
1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the instructions to connect your email account
5. Note down the Service ID (e.g., "service_xxxxxxx")

### Step 3: Create an Email Template
1. In your EmailJS dashboard, go to "Email Templates"
2. Click "Create New Template"
3. Design your email template with the following variables:
   - `{{name}}` - The customer's name
   - `{{email}}` - The customer's email
   - `{{whatsapp}}` - The customer's WhatsApp number (if provided)
   - `{{telegram}}` - The customer's Telegram handle (if provided)
   - `{{songType}}` - The type of song requested
   - `{{songIdea}}` - The customer's song idea description
4. Save the template and note down the Template ID (e.g., "template_xxxxxxx")

### Step 4: Update the Code
1. Open `main.js`
2. Find the EmailJS initialization section and replace `"YOUR_EMAILJS_PUBLIC_KEY"` with your actual public key:
   ```javascript
   emailjs.init("your_actual_public_key");
   ```
3. Find the EmailJS send section and replace the service ID and template ID:
   ```javascript
   emailjs.send("your_service_id", "your_template_id", formData)
   ```

## Testing the Form

1. Fill out the form with test data
2. Submit the form
3. Check that:
   - The loading overlay appears
   - You receive the email with the form data
   - The user is redirected to the Gumroad product page

## Troubleshooting

If the form is not working as expected:

1. Check the browser console for any JavaScript errors
2. Verify that your EmailJS account is active and has not reached its sending limits
3. Confirm that your email service is properly connected
4. Make sure the template variables match the form data object keys

## Gumroad Redirect URL

The current Gumroad product URL is:
```
https://aifrobeats.gumroad.com/l/gpgqic?_gl=1*v2cv6z*_ga****************************.*_ga_6LJN6D94N6*czE3NDc0NDg2NDUkbzEkZzEkdDE3NDc0NTEwODgkajAkbDAkaDA
```

If you need to change this URL, update it in the `main.js` file in the form submission success handler.
