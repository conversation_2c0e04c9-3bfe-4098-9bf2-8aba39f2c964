<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Aifrobeats</title>
    <link rel="icon" href="../images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../styles.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
            font-family: 'Poppins', sans-serif;
        }
        
        .admin-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .sidebar {
            background: rgba(30, 58, 138, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 0.25rem 0;
        }
        
        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 16px;
            color: white;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .request-card {
            background: white;
            border-radius: 16px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .request-card:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-in-progress {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-cancelled {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body class="min-h-screen">
    
    <!-- Admin Login Modal -->
    <div id="admin-login-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="admin-card p-8 max-w-md w-full mx-4">
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-white text-3xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Admin Access</h2>
                <p class="text-gray-600">Enter your admin credentials to continue</p>
            </div>
            
            <form id="admin-login-form" class="space-y-6">
                <div>
                    <label for="admin-email" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-blue-500"></i>Admin Email
                    </label>
                    <input type="email" id="admin-email" required 
                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-all duration-300"
                           placeholder="<EMAIL>">
                </div>
                
                <div>
                    <label for="admin-password" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-blue-500"></i>Password
                    </label>
                    <input type="password" id="admin-password" required 
                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 transition-all duration-300"
                           placeholder="Enter admin password">
                </div>
                
                <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-sign-in-alt mr-2"></i>Access Admin Dashboard
                </button>
            </form>
            
            <div id="login-error" class="hidden mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
                <p class="text-red-700 text-sm"></p>
            </div>
        </div>
    </div>

    <!-- Main Admin Dashboard -->
    <div id="admin-dashboard" class="hidden min-h-screen flex">
        
        <!-- Sidebar -->
        <div class="sidebar w-64 p-6">
            <div class="flex items-center mb-8">
                <img src="../images/mlogo.png" alt="Aifrobeats Logo" class="w-10 h-10 mr-3">
                <div>
                    <h1 class="text-white text-xl font-bold">Aifrobeats</h1>
                    <p class="text-blue-200 text-sm">Admin Panel</p>
                </div>
            </div>
            
            <nav class="space-y-2">
                <a href="#dashboard" class="nav-item active flex items-center px-4 py-3 text-white" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                </a>
                <a href="#requests" class="nav-item flex items-center px-4 py-3 text-white" onclick="showSection('requests')">
                    <i class="fas fa-music mr-3"></i>Song Requests
                </a>
                <a href="#users" class="nav-item flex items-center px-4 py-3 text-white" onclick="showSection('users')">
                    <i class="fas fa-users mr-3"></i>Users
                </a>
                <a href="#uploads" class="nav-item flex items-center px-4 py-3 text-white" onclick="showSection('uploads')">
                    <i class="fas fa-upload mr-3"></i>Upload Files
                </a>
                <a href="#analytics" class="nav-item flex items-center px-4 py-3 text-white" onclick="showSection('analytics')">
                    <i class="fas fa-chart-bar mr-3"></i>Analytics
                </a>
            </nav>
            
            <div class="mt-auto pt-8">
                <button id="admin-logout" class="w-full flex items-center px-4 py-3 text-white hover:bg-red-500 rounded-lg transition-colors">
                    <i class="fas fa-sign-out-alt mr-3"></i>Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="admin-section">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">Admin Dashboard</h2>
                        <p class="text-blue-200">Welcome back! Here's what's happening with Aifrobeats.</p>
                    </div>
                    <div class="text-right text-white">
                        <div class="text-sm text-blue-200">Last updated</div>
                        <div id="last-updated" class="font-semibold">Just now</div>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">Total Requests</p>
                                <p id="total-requests" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-music text-4xl text-green-200"></i>
                        </div>
                    </div>
                    
                    <div class="stat-card p-6" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">Pending</p>
                                <p id="pending-requests" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-clock text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    
                    <div class="stat-card p-6" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100 text-sm">In Progress</p>
                                <p id="inprogress-requests" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-spinner text-4xl text-yellow-200"></i>
                        </div>
                    </div>
                    
                    <div class="stat-card p-6" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">Total Users</p>
                                <p id="total-users" class="text-3xl font-bold">0</p>
                            </div>
                            <i class="fas fa-users text-4xl text-purple-200"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Requests -->
                <div class="admin-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-800">Recent Requests</h3>
                        <button onclick="showSection('requests')" class="text-blue-600 hover:text-blue-700 font-semibold">
                            View All <i class="fas fa-arrow-right ml-1"></i>
                        </button>
                    </div>
                    <div id="recent-requests" class="space-y-4">
                        <!-- Recent requests will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Requests Management Section -->
            <div id="requests-section" class="admin-section hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">Song Requests Management</h2>
                        <p class="text-blue-200">Manage all user song requests and upload completed tracks</p>
                    </div>
                    <button onclick="refreshRequests()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>

                <!-- Filter Tabs -->
                <div class="admin-card p-6 mb-6">
                    <div class="flex flex-wrap gap-2 mb-4">
                        <button onclick="filterRequests('all')" class="filter-btn active bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold">
                            All Requests
                        </button>
                        <button onclick="filterRequests('pending')" class="filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-semibold">
                            Pending
                        </button>
                        <button onclick="filterRequests('in-progress')" class="filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-semibold">
                            In Progress
                        </button>
                        <button onclick="filterRequests('completed')" class="filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-semibold">
                            Completed
                        </button>
                        <button onclick="filterRequests('cancelled')" class="filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-semibold">
                            Cancelled
                        </button>
                    </div>

                    <!-- Search Bar -->
                    <div class="relative">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        <input type="text" id="search-requests" placeholder="Search by song title, user email, or request ID..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500">
                    </div>
                </div>

                <!-- Requests List -->
                <div class="admin-card p-6">
                    <div id="requests-loading" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading requests...</p>
                    </div>

                    <div id="requests-list" class="hidden space-y-4">
                        <!-- Requests will be populated here -->
                    </div>

                    <div id="no-requests" class="hidden text-center py-8">
                        <i class="fas fa-music text-gray-300 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">No requests found</h3>
                        <p class="text-gray-500">No song requests match your current filter.</p>
                    </div>
                </div>
            </div>

            <div id="users-section" class="admin-section hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">User Management</h2>
                        <p class="text-blue-200">Manage user accounts and credit balances</p>
                    </div>
                    <button onclick="refreshUsers()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>

                <!-- User Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Total Users</p>
                                <p id="users-total" class="text-3xl font-bold text-gray-800">0</p>
                            </div>
                            <i class="fas fa-users text-4xl text-blue-500"></i>
                        </div>
                    </div>

                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Active Users</p>
                                <p id="users-active" class="text-3xl font-bold text-green-600">0</p>
                            </div>
                            <i class="fas fa-user-check text-4xl text-green-500"></i>
                        </div>
                    </div>

                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Total Credits</p>
                                <p id="credits-total" class="text-3xl font-bold text-purple-600">0</p>
                            </div>
                            <i class="fas fa-coins text-4xl text-purple-500"></i>
                        </div>
                    </div>
                </div>

                <!-- Users List -->
                <div class="admin-card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-gray-800">All Users</h3>
                        <div class="flex space-x-2">
                            <input type="text" id="search-users" placeholder="Search users..."
                                   class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500">
                            <button onclick="showAddCreditsModal()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-plus mr-2"></i>Add Credits
                            </button>
                        </div>
                    </div>

                    <div id="users-loading" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p class="text-gray-600">Loading users...</p>
                    </div>

                    <div id="users-list" class="hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">User</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Credits</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Requests</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Joined</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="users-table-body">
                                    <!-- Users will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div id="uploads-section" class="admin-section hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">File Upload Center</h2>
                        <p class="text-blue-200">Upload song previews and final tracks for user requests</p>
                    </div>
                </div>

                <!-- Upload Form -->
                <div class="admin-card p-6 mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-6">Upload Song Files</h3>

                    <form id="upload-form" class="space-y-6">
                        <!-- Request Selection -->
                        <div>
                            <label for="request-select" class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-music mr-2 text-blue-500"></i>Select Request
                            </label>
                            <select id="request-select" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500">
                                <option value="">Choose a request to upload files for...</option>
                            </select>
                        </div>

                        <!-- File Type Selection -->
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-file-audio mr-2 text-green-500"></i>File Type
                            </label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="fileType" value="preview" class="mr-2" checked>
                                    <span>Preview (30-60 seconds)</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="fileType" value="final" class="mr-2">
                                    <span>Final Track (Complete song)</span>
                                </label>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div>
                            <label for="audio-file" class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-upload mr-2 text-purple-500"></i>Audio File
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-500 transition-colors">
                                <input type="file" id="audio-file" accept="audio/*" required class="hidden">
                                <div id="upload-area" onclick="document.getElementById('audio-file').click()" class="cursor-pointer">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-600 mb-2">Click to select audio file</p>
                                    <p class="text-sm text-gray-500">Supports MP3, WAV, M4A files (Max 50MB)</p>
                                </div>
                                <div id="file-info" class="hidden">
                                    <i class="fas fa-file-audio text-green-500 text-2xl mb-2"></i>
                                    <p id="file-name" class="font-semibold text-gray-700"></p>
                                    <p id="file-size" class="text-sm text-gray-500"></p>
                                </div>
                            </div>
                        </div>

                        <!-- Upload Notes -->
                        <div>
                            <label for="upload-notes" class="block text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-sticky-note mr-2 text-yellow-500"></i>Notes (Optional)
                            </label>
                            <textarea id="upload-notes" rows="3"
                                      class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500"
                                      placeholder="Add any notes about this upload..."></textarea>
                        </div>

                        <!-- Upload Button -->
                        <button type="submit" id="upload-btn" class="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300">
                            <i class="fas fa-upload mr-2"></i>Upload File
                        </button>
                    </form>

                    <!-- Upload Progress -->
                    <div id="upload-progress" class="hidden mt-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">Uploading...</span>
                            <span id="progress-percent" class="text-sm font-medium text-blue-600">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Recent Uploads -->
                <div class="admin-card p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-6">Recent Uploads</h3>
                    <div id="recent-uploads" class="space-y-4">
                        <!-- Recent uploads will be populated here -->
                    </div>
                </div>
            </div>

            <div id="analytics-section" class="admin-section hidden">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h2 class="text-3xl font-bold text-white mb-2">Analytics & Reports</h2>
                        <p class="text-blue-200">Track performance and user engagement</p>
                    </div>
                    <div class="flex space-x-2">
                        <select id="analytics-period" class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg">
                            <option value="7">Last 7 days</option>
                            <option value="30">Last 30 days</option>
                            <option value="90">Last 90 days</option>
                        </select>
                        <button onclick="refreshAnalytics()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh
                        </button>
                    </div>
                </div>

                <!-- Analytics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Requests This Period</p>
                                <p id="analytics-requests" class="text-3xl font-bold text-blue-600">0</p>
                            </div>
                            <i class="fas fa-chart-line text-4xl text-blue-500"></i>
                        </div>
                    </div>

                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Completion Rate</p>
                                <p id="analytics-completion" class="text-3xl font-bold text-green-600">0%</p>
                            </div>
                            <i class="fas fa-check-circle text-4xl text-green-500"></i>
                        </div>
                    </div>

                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">New Users</p>
                                <p id="analytics-new-users" class="text-3xl font-bold text-purple-600">0</p>
                            </div>
                            <i class="fas fa-user-plus text-4xl text-purple-500"></i>
                        </div>
                    </div>

                    <div class="admin-card p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Avg. Response Time</p>
                                <p id="analytics-response-time" class="text-3xl font-bold text-orange-600">0h</p>
                            </div>
                            <i class="fas fa-clock text-4xl text-orange-500"></i>
                        </div>
                    </div>
                </div>

                <!-- Charts and Reports -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Request Types Chart -->
                    <div class="admin-card p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">Popular Song Types</h3>
                        <div id="song-types-chart" class="space-y-3">
                            <!-- Chart will be populated here -->
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="admin-card p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">Recent Activity</h3>
                        <div id="recent-activity" class="space-y-3">
                            <!-- Activity will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-storage-compat.js"></script>

    <!-- Admin Dashboard Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();
        const storage = firebase.storage();

        let currentAdmin = null;

        // Admin emails (in production, this should be in Firestore)
        const adminEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        // Admin login
        document.getElementById('admin-login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;
            
            if (!adminEmails.includes(email)) {
                showLoginError('Access denied. Admin credentials required.');
                return;
            }
            
            try {
                showLoading(true);
                await auth.signInWithEmailAndPassword(email, password);
            } catch (error) {
                showLoading(false);
                showLoginError('Invalid credentials. Please try again.');
            }
        });

        // Auth state observer
        auth.onAuthStateChanged((user) => {
            if (user && adminEmails.includes(user.email)) {
                currentAdmin = user;
                document.getElementById('admin-login-modal').classList.add('hidden');
                document.getElementById('admin-dashboard').classList.remove('hidden');
                loadDashboardData();
                showLoading(false);
            } else if (user) {
                // Regular user, not admin
                auth.signOut();
                showLoginError('Access denied. Admin credentials required.');
                showLoading(false);
            } else {
                // No user signed in
                document.getElementById('admin-login-modal').classList.remove('hidden');
                document.getElementById('admin-dashboard').classList.add('hidden');
                showLoading(false);
            }
        });

        // Navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.admin-section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').classList.remove('hidden');
            
            // Add active class to clicked nav item
            event.target.closest('.nav-item').classList.add('active');
            
            // Load section-specific data
            if (sectionName === 'requests') {
                loadRequestsData();
            } else if (sectionName === 'users') {
                loadUsersData();
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Load requests stats
                const requestsSnapshot = await db.collection('requests').get();
                const requests = requestsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                
                document.getElementById('total-requests').textContent = requests.length;
                document.getElementById('pending-requests').textContent = requests.filter(r => r.status === 'pending').length;
                document.getElementById('inprogress-requests').textContent = requests.filter(r => r.status === 'in-progress').length;
                
                // Load users stats
                const usersSnapshot = await db.collection('users').get();
                document.getElementById('total-users').textContent = usersSnapshot.size;
                
                // Load recent requests
                loadRecentRequests(requests.slice(0, 5));
                
                // Update timestamp
                document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load recent requests
        function loadRecentRequests(requests) {
            const container = document.getElementById('recent-requests');
            
            if (requests.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No requests yet</p>';
                return;
            }
            
            container.innerHTML = requests.map(request => `
                <div class="request-card p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">${request.songTitle || 'Untitled'}</h4>
                            <p class="text-sm text-gray-600">${request.songType || 'Custom'}</p>
                            <p class="text-xs text-gray-500 mt-1">
                                ${request.createdAt ? new Date(request.createdAt.toDate()).toLocaleDateString() : 'Unknown date'}
                            </p>
                        </div>
                        <span class="status-badge status-${request.status || 'pending'}">${request.status || 'pending'}</span>
                    </div>
                </div>
            `).join('');
        }

        // Utility functions
        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        function showLoginError(message) {
            const errorDiv = document.getElementById('login-error');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');
            setTimeout(() => errorDiv.classList.add('hidden'), 5000);
        }

        // Logout
        document.getElementById('admin-logout').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.reload();
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        // Request management variables
        let allRequests = [];
        let currentFilter = 'all';

        // Load requests data
        async function loadRequestsData() {
            try {
                showRequestsLoading(true);

                const requestsSnapshot = await db.collection('requests')
                    .orderBy('createdAt', 'desc')
                    .get();

                allRequests = requestsSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                displayRequests(allRequests);
                populateRequestSelect();
                showRequestsLoading(false);

            } catch (error) {
                console.error('Error loading requests:', error);
                showRequestsLoading(false);
            }
        }

        // Display requests
        function displayRequests(requests) {
            const container = document.getElementById('requests-list');
            const noRequestsDiv = document.getElementById('no-requests');

            if (requests.length === 0) {
                container.classList.add('hidden');
                noRequestsDiv.classList.remove('hidden');
                return;
            }

            noRequestsDiv.classList.add('hidden');
            container.classList.remove('hidden');

            container.innerHTML = requests.map(request => createRequestCard(request)).join('');
        }

        // Create request card HTML
        function createRequestCard(request) {
            const createdAt = request.createdAt ? new Date(request.createdAt.toDate()).toLocaleDateString() : 'Unknown';
            const userEmail = request.userEmail || request.contactEmail || 'Unknown';

            return `
                <div class="request-card p-6 border border-gray-200 rounded-xl">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h4 class="text-lg font-bold text-gray-800 mr-3">${request.songTitle || 'Untitled Song'}</h4>
                                <span class="status-badge status-${request.status || 'pending'}">${request.status || 'pending'}</span>
                            </div>
                            <p class="text-gray-600 mb-2"><i class="fas fa-tag mr-2"></i>${request.songType || 'Custom'}</p>
                            <p class="text-sm text-gray-500 mb-2"><i class="fas fa-user mr-2"></i>${userEmail}</p>
                            <p class="text-sm text-gray-500"><i class="fas fa-calendar mr-2"></i>Requested: ${createdAt}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-xs text-gray-400 mb-2">ID: ${request.id.substring(0, 8)}...</p>
                            <div class="space-y-2">
                                ${request.status === 'pending' ? `
                                    <button onclick="updateRequestStatus('${request.id}', 'in-progress')"
                                            class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                                        Start Work
                                    </button>
                                ` : ''}
                                ${request.status === 'in-progress' ? `
                                    <button onclick="updateRequestStatus('${request.id}', 'completed')"
                                            class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                        Mark Complete
                                    </button>
                                ` : ''}
                                <button onclick="viewRequestDetails('${request.id}')"
                                        class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                    View Details
                                </button>
                            </div>
                        </div>
                    </div>

                    ${request.description ? `
                        <div class="bg-gray-50 rounded-lg p-3 mb-4">
                            <p class="text-sm text-gray-700"><strong>Description:</strong> ${request.description}</p>
                        </div>
                    ` : ''}

                    ${request.previewUrl || request.downloadUrl ? `
                        <div class="flex space-x-2 mt-4">
                            ${request.previewUrl ? `
                                <a href="${request.previewUrl}" target="_blank"
                                   class="bg-blue-100 text-blue-700 px-3 py-1 rounded text-sm">
                                    <i class="fas fa-play mr-1"></i>Preview
                                </a>
                            ` : ''}
                            ${request.downloadUrl ? `
                                <a href="${request.downloadUrl}" target="_blank"
                                   class="bg-green-100 text-green-700 px-3 py-1 rounded text-sm">
                                    <i class="fas fa-download mr-1"></i>Final Track
                                </a>
                            ` : ''}
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Filter requests
        function filterRequests(status) {
            currentFilter = status;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-700');
            });
            event.target.classList.add('active', 'bg-blue-600', 'text-white');
            event.target.classList.remove('bg-gray-200', 'text-gray-700');

            // Filter and display requests
            let filteredRequests = allRequests;
            if (status !== 'all') {
                filteredRequests = allRequests.filter(request => request.status === status);
            }

            displayRequests(filteredRequests);
        }

        // Update request status
        async function updateRequestStatus(requestId, newStatus) {
            try {
                await db.collection('requests').doc(requestId).update({
                    status: newStatus,
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                });

                // Refresh the requests list
                loadRequestsData();

                // Show success message
                alert(`Request status updated to: ${newStatus}`);

            } catch (error) {
                console.error('Error updating request status:', error);
                alert('Error updating request status. Please try again.');
            }
        }

        // View request details
        function viewRequestDetails(requestId) {
            const request = allRequests.find(r => r.id === requestId);
            if (!request) return;

            const details = `
                Song Title: ${request.songTitle || 'Untitled'}
                Song Type: ${request.songType || 'Custom'}
                User Email: ${request.userEmail || request.contactEmail || 'Unknown'}
                Status: ${request.status || 'pending'}

                Description:
                ${request.description || 'No description provided'}

                ${request.specialInstructions ? `Special Instructions:
                ${request.specialInstructions}` : ''}

                Request ID: ${request.id}
                Created: ${request.createdAt ? new Date(request.createdAt.toDate()).toLocaleString() : 'Unknown'}
            `;

            alert(details);
        }

        // Populate request select dropdown
        function populateRequestSelect() {
            const select = document.getElementById('request-select');
            const pendingAndInProgress = allRequests.filter(r =>
                r.status === 'pending' || r.status === 'in-progress'
            );

            select.innerHTML = '<option value="">Choose a request to upload files for...</option>';

            pendingAndInProgress.forEach(request => {
                const option = document.createElement('option');
                option.value = request.id;
                option.textContent = `${request.songTitle || 'Untitled'} - ${request.userEmail || 'Unknown User'}`;
                select.appendChild(option);
            });
        }

        // Refresh requests
        function refreshRequests() {
            loadRequestsData();
        }

        // Show/hide requests loading
        function showRequestsLoading(show = true) {
            const loading = document.getElementById('requests-loading');
            const list = document.getElementById('requests-list');
            const noRequests = document.getElementById('no-requests');

            if (show) {
                loading.classList.remove('hidden');
                list.classList.add('hidden');
                noRequests.classList.add('hidden');
            } else {
                loading.classList.add('hidden');
            }
        }

        // File upload functionality
        document.getElementById('audio-file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('upload-area').classList.add('hidden');
                document.getElementById('file-info').classList.remove('hidden');
                document.getElementById('file-name').textContent = file.name;
                document.getElementById('file-size').textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
            }
        });

        // Handle file upload form
        document.getElementById('upload-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const requestId = document.getElementById('request-select').value;
            const fileType = document.querySelector('input[name="fileType"]:checked').value;
            const file = document.getElementById('audio-file').files[0];
            const notes = document.getElementById('upload-notes').value;

            if (!requestId || !file) {
                alert('Please select a request and choose a file to upload.');
                return;
            }

            try {
                // Show upload progress
                document.getElementById('upload-progress').classList.remove('hidden');
                document.getElementById('upload-btn').disabled = true;

                // Create storage reference
                const fileName = `${requestId}_${fileType}_${Date.now()}_${file.name}`;
                const storageRef = storage.ref(`songs/${fileName}`);

                // Upload file with progress tracking
                const uploadTask = storageRef.put(file);

                uploadTask.on('state_changed',
                    (snapshot) => {
                        // Progress tracking
                        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
                        document.getElementById('progress-bar').style.width = progress + '%';
                        document.getElementById('progress-percent').textContent = Math.round(progress) + '%';
                    },
                    (error) => {
                        console.error('Upload error:', error);
                        alert('Upload failed. Please try again.');
                        resetUploadForm();
                    },
                    async () => {
                        // Upload completed successfully
                        try {
                            const downloadURL = await uploadTask.snapshot.ref.getDownloadURL();

                            // Update request document with file URL
                            const updateData = {
                                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                            };

                            if (fileType === 'preview') {
                                updateData.previewUrl = downloadURL;
                                updateData.status = 'in-progress';
                            } else {
                                updateData.downloadUrl = downloadURL;
                                updateData.status = 'completed';
                            }

                            if (notes) {
                                updateData.adminNotes = notes;
                            }

                            await db.collection('requests').doc(requestId).update(updateData);

                            alert(`${fileType === 'preview' ? 'Preview' : 'Final track'} uploaded successfully!`);
                            resetUploadForm();
                            loadRequestsData(); // Refresh requests list

                        } catch (error) {
                            console.error('Error updating request:', error);
                            alert('File uploaded but failed to update request. Please check manually.');
                            resetUploadForm();
                        }
                    }
                );

            } catch (error) {
                console.error('Upload error:', error);
                alert('Upload failed. Please try again.');
                resetUploadForm();
            }
        });

        // Reset upload form
        function resetUploadForm() {
            document.getElementById('upload-form').reset();
            document.getElementById('upload-progress').classList.add('hidden');
            document.getElementById('upload-btn').disabled = false;
            document.getElementById('upload-area').classList.remove('hidden');
            document.getElementById('file-info').classList.add('hidden');
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-percent').textContent = '0%';
        }

        // Search functionality
        document.getElementById('search-requests').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            let filteredRequests = allRequests;

            if (currentFilter !== 'all') {
                filteredRequests = allRequests.filter(request => request.status === currentFilter);
            }

            if (searchTerm) {
                filteredRequests = filteredRequests.filter(request =>
                    (request.songTitle || '').toLowerCase().includes(searchTerm) ||
                    (request.userEmail || '').toLowerCase().includes(searchTerm) ||
                    (request.contactEmail || '').toLowerCase().includes(searchTerm) ||
                    request.id.toLowerCase().includes(searchTerm)
                );
            }

            displayRequests(filteredRequests);
        });

        // User management variables
        let allUsers = [];

        // Load users data
        async function loadUsersData() {
            try {
                document.getElementById('users-loading').classList.remove('hidden');
                document.getElementById('users-list').classList.add('hidden');

                const usersSnapshot = await db.collection('users').get();
                allUsers = usersSnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));

                // Calculate stats
                const totalUsers = allUsers.length;
                const activeUsers = allUsers.filter(user => user.lastRequestAt).length;
                const totalCredits = allUsers.reduce((sum, user) => sum + (user.credits || 0), 0);

                document.getElementById('users-total').textContent = totalUsers;
                document.getElementById('users-active').textContent = activeUsers;
                document.getElementById('credits-total').textContent = totalCredits;

                // Display users table
                displayUsersTable(allUsers);

                document.getElementById('users-loading').classList.add('hidden');
                document.getElementById('users-list').classList.remove('hidden');

            } catch (error) {
                console.error('Error loading users:', error);
                document.getElementById('users-loading').classList.add('hidden');
            }
        }

        // Display users table
        function displayUsersTable(users) {
            const tbody = document.getElementById('users-table-body');

            tbody.innerHTML = users.map(user => {
                const joinedDate = user.createdAt ? new Date(user.createdAt.toDate()).toLocaleDateString() : 'Unknown';
                const userRequests = allRequests.filter(r => r.userId === user.id).length;

                return `
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-3 px-4">
                            <div>
                                <div class="font-semibold text-gray-800">${user.email}</div>
                                <div class="text-sm text-gray-500">ID: ${user.id.substring(0, 8)}...</div>
                            </div>
                        </td>
                        <td class="py-3 px-4">
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                                ${user.credits || 0}
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="text-gray-700">${userRequests}</span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="text-gray-600 text-sm">${joinedDate}</span>
                        </td>
                        <td class="py-3 px-4">
                            <div class="flex space-x-2">
                                <button onclick="addCreditsToUser('${user.id}', '${user.email}')"
                                        class="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs">
                                    Add Credits
                                </button>
                                <button onclick="viewUserDetails('${user.id}')"
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs">
                                    View
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Add credits to user
        function addCreditsToUser(userId, userEmail) {
            const credits = prompt(`How many credits would you like to add to ${userEmail}?`, '1');
            if (credits && !isNaN(credits) && parseInt(credits) > 0) {
                updateUserCredits(userId, parseInt(credits));
            }
        }

        // Update user credits
        async function updateUserCredits(userId, creditsToAdd) {
            try {
                await db.collection('users').doc(userId).update({
                    credits: firebase.firestore.FieldValue.increment(creditsToAdd)
                });

                alert(`Successfully added ${creditsToAdd} credits to user.`);
                loadUsersData(); // Refresh users list

            } catch (error) {
                console.error('Error updating user credits:', error);
                alert('Error updating user credits. Please try again.');
            }
        }

        // View user details
        function viewUserDetails(userId) {
            const user = allUsers.find(u => u.id === userId);
            if (!user) return;

            const userRequests = allRequests.filter(r => r.userId === userId);

            const details = `
                User Details:
                Email: ${user.email}
                Credits: ${user.credits || 0}
                Total Requests: ${userRequests.length}
                Joined: ${user.createdAt ? new Date(user.createdAt.toDate()).toLocaleString() : 'Unknown'}
                Last Request: ${user.lastRequestAt ? new Date(user.lastRequestAt.toDate()).toLocaleString() : 'Never'}

                User ID: ${user.id}
            `;

            alert(details);
        }

        // Refresh users
        function refreshUsers() {
            loadUsersData();
        }

        // Search users
        document.getElementById('search-users').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const filteredUsers = allUsers.filter(user =>
                user.email.toLowerCase().includes(searchTerm) ||
                user.id.toLowerCase().includes(searchTerm)
            );
            displayUsersTable(filteredUsers);
        });

        // Analytics functions
        function refreshAnalytics() {
            loadAnalyticsData();
        }

        async function loadAnalyticsData() {
            try {
                const period = parseInt(document.getElementById('analytics-period').value);
                const periodStart = new Date();
                periodStart.setDate(periodStart.getDate() - period);

                // Filter requests by period
                const periodRequests = allRequests.filter(request => {
                    if (!request.createdAt) return false;
                    return new Date(request.createdAt.toDate()) >= periodStart;
                });

                // Calculate metrics
                const totalRequests = periodRequests.length;
                const completedRequests = periodRequests.filter(r => r.status === 'completed').length;
                const completionRate = totalRequests > 0 ? Math.round((completedRequests / totalRequests) * 100) : 0;

                // New users in period
                const newUsers = allUsers.filter(user => {
                    if (!user.createdAt) return false;
                    return new Date(user.createdAt.toDate()) >= periodStart;
                }).length;

                // Update analytics display
                document.getElementById('analytics-requests').textContent = totalRequests;
                document.getElementById('analytics-completion').textContent = completionRate + '%';
                document.getElementById('analytics-new-users').textContent = newUsers;
                document.getElementById('analytics-response-time').textContent = '2h'; // Placeholder

                // Load song types chart
                loadSongTypesChart(periodRequests);
                loadRecentActivity();

            } catch (error) {
                console.error('Error loading analytics:', error);
            }
        }

        // Load song types chart
        function loadSongTypesChart(requests) {
            const songTypes = {};
            requests.forEach(request => {
                const type = request.songType || 'custom';
                songTypes[type] = (songTypes[type] || 0) + 1;
            });

            const chartContainer = document.getElementById('song-types-chart');
            const total = requests.length;

            if (total === 0) {
                chartContainer.innerHTML = '<p class="text-gray-500 text-center">No data available</p>';
                return;
            }

            chartContainer.innerHTML = Object.entries(songTypes)
                .sort(([,a], [,b]) => b - a)
                .map(([type, count]) => {
                    const percentage = Math.round((count / total) * 100);
                    return `
                        <div class="flex items-center justify-between py-2">
                            <span class="capitalize text-gray-700">${type.replace('-', ' ')}</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: ${percentage}%"></div>
                                </div>
                                <span class="text-sm text-gray-600 w-8">${count}</span>
                            </div>
                        </div>
                    `;
                }).join('');
        }

        // Load recent activity
        function loadRecentActivity() {
            const recentRequests = allRequests
                .sort((a, b) => {
                    const aTime = a.updatedAt || a.createdAt;
                    const bTime = b.updatedAt || b.createdAt;
                    if (!aTime || !bTime) return 0;
                    return bTime.toDate() - aTime.toDate();
                })
                .slice(0, 10);

            const activityContainer = document.getElementById('recent-activity');

            if (recentRequests.length === 0) {
                activityContainer.innerHTML = '<p class="text-gray-500 text-center">No recent activity</p>';
                return;
            }

            activityContainer.innerHTML = recentRequests.map(request => {
                const time = request.updatedAt || request.createdAt;
                const timeStr = time ? new Date(time.toDate()).toLocaleString() : 'Unknown';

                return `
                    <div class="flex items-center space-x-3 py-2">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-800">${request.songTitle || 'Untitled'}</p>
                            <p class="text-xs text-gray-500">${timeStr}</p>
                        </div>
                        <span class="status-badge status-${request.status || 'pending'} text-xs">${request.status || 'pending'}</span>
                    </div>
                `;
            }).join('');
        }
    </script>
</body>
</html>
