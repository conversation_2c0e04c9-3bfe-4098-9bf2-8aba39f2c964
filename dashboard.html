<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">

    <style>
        /* Enhanced Dashboard Styles */
        .filter-btn {
            @apply px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300;
            @apply bg-gray-100 text-gray-600 hover:bg-gray-200;
        }

        .filter-btn.active {
            @apply bg-green-500 text-white shadow-md;
        }

        .request-card {
            @apply bg-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-md transition-all duration-300;
        }

        .status-badge {
            @apply px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide;
        }

        .status-pending {
            @apply bg-yellow-100 text-yellow-800;
        }

        .status-in-progress {
            @apply bg-blue-100 text-blue-800;
        }

        .status-completed {
            @apply bg-green-100 text-green-800;
        }

        .status-cancelled {
            @apply bg-red-100 text-red-800;
        }

        .audio-player {
            @apply bg-gradient-to-r from-green-400 to-blue-500 rounded-xl p-6 text-white;
        }

        .audio-controls {
            @apply flex items-center space-x-4;
        }

        .play-btn {
            @apply w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300;
        }

        .progress-bar {
            @apply flex-1 bg-white bg-opacity-20 rounded-full h-2 overflow-hidden;
        }

        .progress-fill {
            @apply bg-white h-full transition-all duration-300;
        }
    </style>
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary);">
    
    <!-- Navigation Header -->
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg);">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <span id="user-email" class="text-gray-600 hidden md:block"></span>
                <button id="logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-1"></i>Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">

        <!-- Enhanced Welcome Section -->
        <div class="mb-8">
            <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl p-8 text-white shadow-xl">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-4xl font-bold mb-2">Welcome Back!</h1>
                        <p class="text-green-100 text-lg">Ready to create some amazing Afrobeats?</p>
                    </div>
                    <div class="hidden md:block">
                        <div class="bg-white bg-opacity-20 rounded-full p-6">
                            <i class="fas fa-music text-4xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Stats Row -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold" id="dashboard-credits">0</div>
                        <div class="text-sm text-green-100">Available Credits</div>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold" id="total-requests">0</div>
                        <div class="text-sm text-green-100">Total Requests</div>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold" id="completed-requests">0</div>
                        <div class="text-sm text-green-100">Completed</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Quick Actions -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <!-- Request New Song Card -->
            <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                <div class="bg-gradient-to-br from-green-400 to-green-600 p-6 text-white">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-plus-circle text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Request New Song</h3>
                    <p class="text-green-100">Create your custom Afrobeats track</p>
                </div>
                <div class="p-6">
                    <p class="text-gray-600 mb-4">Use your credits to request personalized music</p>
                    <a href="request-form.html" class="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-300 flex items-center justify-center">
                        <i class="fas fa-music mr-2"></i>Start Request
                    </a>
                </div>
            </div>

            <!-- Browse Library Card -->
            <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                <div class="bg-gradient-to-br from-blue-400 to-blue-600 p-6 text-white">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-headphones text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Browse Library</h3>
                    <p class="text-blue-100">Discover our music collection</p>
                </div>
                <div class="p-6">
                    <p class="text-gray-600 mb-4">Listen to our curated Afrobeats library</p>
                    <a href="music-library.html" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-300 flex items-center justify-center">
                        <i class="fas fa-library-music mr-2"></i>Browse Music
                    </a>
                </div>
            </div>

            <!-- Buy Credits Card -->
            <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                <div class="bg-gradient-to-br from-purple-400 to-purple-600 p-6 text-white">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-credit-card text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Buy More Credits</h3>
                    <p class="text-purple-100">Get additional preview credits</p>
                </div>
                <div class="p-6">
                    <p class="text-gray-600 mb-4">Purchase more credits for song requests</p>
                    <button onclick="buyCredits()" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-300 flex items-center justify-center">
                        <i class="fas fa-shopping-cart mr-2"></i>Buy Credits
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced My Requests Section -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">My Song Requests</h2>
                    <p class="text-gray-600">Track the progress of your custom music requests</p>
                </div>
                <button id="refresh-requests" class="bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-lg transition-colors duration-300 flex items-center">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                </button>
            </div>

            <!-- Request Status Filter -->
            <div class="flex flex-wrap gap-2 mb-6">
                <button class="filter-btn active" data-status="all">
                    <i class="fas fa-list mr-2"></i>All Requests
                </button>
                <button class="filter-btn" data-status="pending">
                    <i class="fas fa-clock mr-2"></i>Pending
                </button>
                <button class="filter-btn" data-status="in-progress">
                    <i class="fas fa-spinner mr-2"></i>In Progress
                </button>
                <button class="filter-btn" data-status="completed">
                    <i class="fas fa-check-circle mr-2"></i>Completed
                </button>
            </div>

            <!-- Requests Container -->
            <div id="requests-container">
                <!-- Loading State -->
                <div id="requests-loading" class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
                    <p class="text-gray-600 text-lg">Loading your requests...</p>
                </div>

                <!-- Empty State -->
                <div id="requests-empty" class="text-center py-12 hidden">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-music text-4xl text-gray-400"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-800 mb-3">No requests yet</h3>
                    <p class="text-gray-600 mb-6 max-w-md mx-auto">Start your musical journey by creating your first custom Afrobeats song request</p>
                    <a href="request-form.html" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 inline-flex items-center">
                        <i class="fas fa-plus mr-2"></i>Create First Request
                    </a>
                </div>

                <!-- Requests List -->
                <div id="requests-list" class="space-y-6 hidden">
                    <!-- Requests will be populated here -->
                </div>
            </div>
        </div>

        <!-- Audio Player Section -->
        <div id="audio-player-section" class="bg-white rounded-2xl shadow-lg p-8 mt-8 hidden">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">Now Playing</h3>
            <div id="audio-player-container">
                <!-- Audio player will be inserted here -->
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer id="contact" class="site-footer bg-gray-900 text-gray-400 py-12 md:py-16">
        <div class="container mx-auto px-4 md:px-6 footer-container">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-10">
                <!-- Company Info -->
                <div class="footer-col">
                    <h3 class="text-xl md:text-2xl font-semibold text-white mb-4 flex items-center">
                        <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-2">
                        <span class="animated-gradient-text">Aifrobeats</span>
                    </h3>
                    <p class="mb-4 text-sm md:text-base">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-3">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:<EMAIL>" class="hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-3"></i>
                        <a href="tel:+2347038808350" class="hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Quick Links</h3>
                    <ul class="space-y-2 footer-links">
                        <li>
                            <a href="index.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="dashboard.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="request-form.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Request Song
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Connect With Us</h3>
                    <p class="mb-3 text-sm md:text-base">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg newsletter-form">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="form-input bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-6 md:mt-8 pt-5 md:pt-6 border-t border-gray-700 text-center text-xs md:text-sm">
                <p>&copy; <span id="currentYear"></span> AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline">|</span>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Dashboard Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let requestsListener = null;

        // Auth state observer
        auth.onAuthStateChanged((user) => {
            if (user) {
                currentUser = user;
                document.getElementById('user-email').textContent = user.email;
                console.log('User authenticated:', user.email);
                loadUserData();
                loadUserRequests();
            } else {
                // User is not signed in, redirect to login
                console.log('User not authenticated, redirecting to login');
                window.location.href = 'login.html';
            }
        });

        // Enhanced real-time user data loading
        function loadUserData() {
            if (!currentUser) return;

            // Set up real-time listener for user data
            const userDocRef = db.collection('users').doc(currentUser.uid);
            userDocRef.onSnapshot(async (doc) => {
                try {
                    if (doc.exists) {
                        const userData = doc.data();
                        const credits = userData.credits || 0;

                        // Update multiple credit displays
                        document.getElementById('user-credits').textContent = credits;
                        document.getElementById('dashboard-credits').textContent = credits;

                        console.log('User credits updated:', credits);
                    } else {
                        console.log('User document does not exist, creating...');
                        // Create user document with 3 credits if it doesn't exist
                        await userDocRef.set({
                            email: currentUser.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp()
                        });
                    }
                } catch (error) {
                    console.error('Error in user data snapshot:', error);
                }
            }, (error) => {
                console.error('Error loading user data:', error);
                alert('Error loading your account data. Please refresh the page.');
            });
        }

        // Enhanced user requests loading with real-time updates
        function loadUserRequests() {
            const requestsContainer = document.getElementById('requests-container');
            const loadingDiv = document.getElementById('requests-loading');
            const emptyDiv = document.getElementById('requests-empty');
            const listDiv = document.getElementById('requests-list');

            // Show loading
            loadingDiv.classList.remove('hidden');
            emptyDiv.classList.add('hidden');
            listDiv.classList.add('hidden');

            // Listen to user's requests with real-time updates
            requestsListener = db.collection('requests')
                .where('userId', '==', currentUser.uid)
                .orderBy('createdAt', 'desc')
                .onSnapshot((querySnapshot) => {
                    loadingDiv.classList.add('hidden');

                    if (querySnapshot.empty) {
                        emptyDiv.classList.remove('hidden');
                        listDiv.classList.add('hidden');
                        updateRequestStats(0, 0);
                    } else {
                        emptyDiv.classList.add('hidden');
                        listDiv.classList.remove('hidden');

                        // Clear existing requests
                        listDiv.innerHTML = '';

                        let totalRequests = 0;
                        let completedRequests = 0;

                        // Add each request
                        querySnapshot.forEach((doc) => {
                            const request = doc.data();
                            const requestElement = createRequestElement(doc.id, request);
                            listDiv.appendChild(requestElement);

                            totalRequests++;
                            if (request.status === 'completed') {
                                completedRequests++;
                            }
                        });

                        updateRequestStats(totalRequests, completedRequests);
                    }
                }, (error) => {
                    console.error('Error loading requests:', error);
                    loadingDiv.classList.add('hidden');
                    emptyDiv.classList.remove('hidden');
                });
        }

        // Update request statistics
        function updateRequestStats(total, completed) {
            document.getElementById('total-requests').textContent = total;
            document.getElementById('completed-requests').textContent = completed;
        }

        // Enhanced request element creation
        function createRequestElement(id, request) {
            const div = document.createElement('div');
            div.className = 'request-card';

            const statusIcons = {
                'pending': 'fas fa-clock',
                'in-progress': 'fas fa-spinner fa-spin',
                'completed': 'fas fa-check-circle',
                'cancelled': 'fas fa-times-circle'
            };

            const createdAt = request.createdAt ? new Date(request.createdAt.toDate()).toLocaleDateString() : 'Unknown';
            const updatedAt = request.updatedAt ? new Date(request.updatedAt.toDate()).toLocaleDateString() : createdAt;

            div.innerHTML = `
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h3 class="font-bold text-xl text-gray-800 mb-2">${request.songTitle || 'Custom Song Request'}</h3>
                        <p class="text-gray-600 mb-3">${request.description || 'No description provided'}</p>
                    </div>
                    <div class="ml-4 text-right">
                        <span class="status-badge status-${request.status || 'pending'}">
                            <i class="${statusIcons[request.status] || statusIcons['pending']} mr-1"></i>
                            ${(request.status || 'pending').replace('-', ' ')}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-calendar-alt mr-2 text-gray-400"></i>
                        <span>Requested: ${createdAt}</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-clock mr-2 text-gray-400"></i>
                        <span>Updated: ${updatedAt}</span>
                    </div>
                </div>

                ${request.previewUrl ? `
                    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-music text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Preview Available</h4>
                                    <p class="text-sm text-gray-600">Your custom track is ready to listen</p>
                                </div>
                            </div>
                            <button onclick="playAudio('${request.previewUrl}', '${request.songTitle || 'Custom Song'}')"
                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-300 flex items-center">
                                <i class="fas fa-play mr-2"></i>Play Preview
                            </button>
                        </div>
                    </div>
                ` : ''}

                <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                    <div class="text-xs text-gray-500">
                        Request ID: ${id.substring(0, 8)}...
                    </div>
                    <div class="flex space-x-2">
                        ${request.status === 'completed' && request.downloadUrl ? `
                            <a href="${request.downloadUrl}" download
                               class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors duration-300">
                                <i class="fas fa-download mr-1"></i>Download
                            </a>
                        ` : ''}
                        ${request.status === 'pending' ? `
                            <button onclick="cancelRequest('${id}')"
                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors duration-300">
                                <i class="fas fa-times mr-1"></i>Cancel
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            return div;
        }

        // Logout function
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        // Refresh requests
        document.getElementById('refresh-requests').addEventListener('click', () => {
            loadUserRequests();
        });

        // Audio player functionality
        let currentAudio = null;

        function playAudio(url, title) {
            // Stop current audio if playing
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }

            // Show audio player section
            const playerSection = document.getElementById('audio-player-section');
            const playerContainer = document.getElementById('audio-player-container');

            playerSection.classList.remove('hidden');

            // Create audio player
            playerContainer.innerHTML = `
                <div class="audio-player">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="text-xl font-bold text-white">${title}</h4>
                            <p class="text-white text-opacity-80">Preview Track</p>
                        </div>
                        <button onclick="closeAudioPlayer()" class="text-white hover:text-red-300 transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="audio-controls">
                        <button id="play-pause-btn" onclick="togglePlayPause()" class="play-btn">
                            <i class="fas fa-play"></i>
                        </button>
                        <div class="progress-bar" onclick="seekAudio(event)">
                            <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="text-white text-sm">
                            <span id="current-time">0:00</span> / <span id="duration">0:00</span>
                        </div>
                    </div>
                    <audio id="audio-element" src="${url}" preload="metadata"></audio>
                </div>
            `;

            // Initialize audio element
            currentAudio = document.getElementById('audio-element');
            setupAudioEvents();
        }

        function setupAudioEvents() {
            const audio = currentAudio;
            const playPauseBtn = document.getElementById('play-pause-btn');
            const progressFill = document.getElementById('progress-fill');
            const currentTimeSpan = document.getElementById('current-time');
            const durationSpan = document.getElementById('duration');

            audio.addEventListener('loadedmetadata', () => {
                durationSpan.textContent = formatTime(audio.duration);
            });

            audio.addEventListener('timeupdate', () => {
                const progress = (audio.currentTime / audio.duration) * 100;
                progressFill.style.width = progress + '%';
                currentTimeSpan.textContent = formatTime(audio.currentTime);
            });

            audio.addEventListener('ended', () => {
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            });
        }

        function togglePlayPause() {
            const audio = currentAudio;
            const playPauseBtn = document.getElementById('play-pause-btn');

            if (audio.paused) {
                audio.play();
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                audio.pause();
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        }

        function seekAudio(event) {
            const progressBar = event.currentTarget;
            const clickX = event.offsetX;
            const width = progressBar.offsetWidth;
            const duration = currentAudio.duration;

            currentAudio.currentTime = (clickX / width) * duration;
        }

        function closeAudioPlayer() {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }
            document.getElementById('audio-player-section').classList.add('hidden');
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // Filter functionality
        document.addEventListener('DOMContentLoaded', () => {
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    // Remove active class from all buttons
                    filterBtns.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    btn.classList.add('active');

                    const status = btn.dataset.status;
                    filterRequests(status);
                });
            });
        });

        function filterRequests(status) {
            const requestCards = document.querySelectorAll('.request-card');
            requestCards.forEach(card => {
                if (status === 'all') {
                    card.style.display = 'block';
                } else {
                    const statusBadge = card.querySelector('.status-badge');
                    const cardStatus = statusBadge.textContent.toLowerCase().trim().replace(' ', '-');
                    card.style.display = cardStatus.includes(status) ? 'block' : 'none';
                }
            });
        }

        // Cancel request functionality
        async function cancelRequest(requestId) {
            if (confirm('Are you sure you want to cancel this request?')) {
                try {
                    await db.collection('requests').doc(requestId).update({
                        status: 'cancelled',
                        updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                    });
                    console.log('Request cancelled successfully');
                } catch (error) {
                    console.error('Error cancelling request:', error);
                    alert('Error cancelling request. Please try again.');
                }
            }
        }

        // Buy credits function (placeholder)
        function buyCredits() {
            alert('Credit purchase feature coming soon! Contact support for now.');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (requestsListener) {
                requestsListener();
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
