<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Authentication - Aifrobeats</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-section {
            background: #1f2937;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .debug-log {
            background: #111827;
            color: #10b981;
            font-family: monospace;
            padding: 1rem;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-6">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6 text-green-400">Authentication Debug Tool</h1>
        
        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Current User Status</h2>
            <div id="user-status" class="debug-log">Loading...</div>
        </div>

        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            <div class="space-x-4">
                <button id="check-user" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">Check Current User</button>
                <button id="fix-user-doc" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">Fix User Document</button>
                <button id="sign-out" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">Sign Out</button>
                <button id="delete-account" class="bg-red-800 hover:bg-red-900 px-4 py-2 rounded">Delete Account</button>
            </div>
        </div>

        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Debug Log</h2>
            <div id="debug-log" class="debug-log">Debug information will appear here...</div>
        </div>

        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Test Email/Password Creation</h2>
            <div class="space-y-4">
                <input type="email" id="test-email" placeholder="Test email" class="w-full p-2 bg-gray-800 border border-gray-600 rounded">
                <input type="password" id="test-password" placeholder="Test password" class="w-full p-2 bg-gray-800 border border-gray-600 rounded">
                <button id="create-test-user" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">Create Test User</button>
            </div>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        const userStatusDiv = document.getElementById('user-status');
        const debugLogDiv = document.getElementById('debug-log');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLogDiv.textContent += `[${timestamp}] ${message}\n`;
            debugLogDiv.scrollTop = debugLogDiv.scrollHeight;
            console.log(message);
        }

        function updateUserStatus(user) {
            if (user) {
                userStatusDiv.textContent = `
Signed in as: ${user.email}
UID: ${user.uid}
Email Verified: ${user.emailVerified}
Creation Time: ${user.metadata.creationTime}
Last Sign In: ${user.metadata.lastSignInTime}
Provider Data: ${JSON.stringify(user.providerData, null, 2)}
                `;
            } else {
                userStatusDiv.textContent = 'No user signed in';
            }
        }

        // Monitor auth state
        auth.onAuthStateChanged((user) => {
            updateUserStatus(user);
            if (user) {
                log(`User signed in: ${user.email} (${user.uid})`);
                checkUserDocument(user);
            } else {
                log('No user signed in');
            }
        });

        async function checkUserDocument(user) {
            try {
                const userDoc = await db.collection('users').doc(user.uid).get();
                if (userDoc.exists) {
                    log(`User document exists: ${JSON.stringify(userDoc.data(), null, 2)}`);
                } else {
                    log('User document does NOT exist');
                }
            } catch (error) {
                log(`Error checking user document: ${error.message}`);
            }
        }

        // Button handlers
        document.getElementById('check-user').addEventListener('click', () => {
            const user = auth.currentUser;
            if (user) {
                log('Checking current user...');
                updateUserStatus(user);
                checkUserDocument(user);
            } else {
                log('No current user');
            }
        });

        document.getElementById('fix-user-doc').addEventListener('click', async () => {
            const user = auth.currentUser;
            if (!user) {
                log('No user signed in');
                return;
            }

            try {
                log('Creating/updating user document...');
                await db.collection('users').doc(user.uid).set({
                    email: user.email,
                    credits: 3,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    requests: [],
                    profile: {
                        displayName: user.displayName || '',
                        photoURL: user.photoURL || ''
                    }
                }, { merge: true });
                log('User document created/updated successfully');
            } catch (error) {
                log(`Error creating user document: ${error.message}`);
            }
        });

        document.getElementById('sign-out').addEventListener('click', async () => {
            try {
                await auth.signOut();
                log('User signed out');
            } catch (error) {
                log(`Error signing out: ${error.message}`);
            }
        });

        document.getElementById('delete-account').addEventListener('click', async () => {
            const user = auth.currentUser;
            if (!user) {
                log('No user signed in');
                return;
            }

            if (confirm('Are you sure you want to delete this account? This cannot be undone.')) {
                try {
                    // Delete user document first
                    await db.collection('users').doc(user.uid).delete();
                    log('User document deleted');
                    
                    // Delete auth account
                    await user.delete();
                    log('User account deleted');
                } catch (error) {
                    log(`Error deleting account: ${error.message}`);
                }
            }
        });

        document.getElementById('create-test-user').addEventListener('click', async () => {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;

            if (!email || !password) {
                log('Please enter both email and password');
                return;
            }

            try {
                log(`Creating test user: ${email}`);
                const userCredential = await auth.createUserWithEmailAndPassword(email, password);
                const user = userCredential.user;
                
                log(`Test user created: ${user.uid}`);
                
                // Create user document
                await db.collection('users').doc(user.uid).set({
                    email: user.email,
                    credits: 3,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    requests: [],
                    profile: {
                        displayName: '',
                        photoURL: ''
                    }
                });
                
                log('Test user document created');
            } catch (error) {
                log(`Error creating test user: ${error.message}`);
            }
        });

        log('Debug tool loaded');
    </script>
</body>
</html>
