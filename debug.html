<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Debug - Aifrobeats</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-6 max-w-4xl">
        <h1 class="text-3xl font-bold mb-8 text-center">Firebase Debug Console</h1>
        
        <!-- Firebase Status -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Firebase Connection Status</h2>
            <div id="firebase-status" class="space-y-2">
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-gray-400 rounded-full mr-3"></span>
                    <span>Checking Firebase connection...</span>
                </div>
            </div>
        </div>

        <!-- User Info -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Current User</h2>
            <div id="user-info">
                <p class="text-gray-600">Loading user information...</p>
            </div>
        </div>

        <!-- User Document -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">User Document in Firestore</h2>
            <div id="user-document">
                <p class="text-gray-600">Loading user document...</p>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Debug Actions</h2>
            <div class="space-y-3">
                <button id="create-user-doc" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Create User Document with 3 Credits
                </button>
                <button id="refresh-data" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Refresh All Data
                </button>
                <button id="test-firestore" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test Firestore Write
                </button>
            </div>
        </div>

        <!-- Console Log -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Console Log</h2>
            <div id="console-log" class="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
                <div>Debug console initialized...</div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="text-center mt-8">
            <a href="index.html" class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700 mr-4">
                Back to Home
            </a>
            <a href="dashboard.html" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">
                Go to Dashboard
            </a>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;

        // Console logging
        function log(message, type = 'info') {
            const consoleLog = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-blue-400';
            consoleLog.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            consoleLog.scrollTop = consoleLog.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        // Update Firebase status
        function updateFirebaseStatus() {
            const statusDiv = document.getElementById('firebase-status');
            statusDiv.innerHTML = `
                <div class="flex items-center mb-2">
                    <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                    <span>Firebase App initialized</span>
                </div>
                <div class="flex items-center mb-2">
                    <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                    <span>Auth service connected</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                    <span>Firestore service connected</span>
                </div>
            `;
            log('Firebase services initialized successfully', 'success');
        }

        // Update user info
        function updateUserInfo(user) {
            const userInfoDiv = document.getElementById('user-info');
            if (user) {
                userInfoDiv.innerHTML = `
                    <div class="space-y-2">
                        <p><strong>Email:</strong> ${user.email}</p>
                        <p><strong>UID:</strong> ${user.uid}</p>
                        <p><strong>Email Verified:</strong> ${user.emailVerified}</p>
                        <p><strong>Created:</strong> ${new Date(user.metadata.creationTime).toLocaleString()}</p>
                        <p><strong>Last Sign In:</strong> ${new Date(user.metadata.lastSignInTime).toLocaleString()}</p>
                    </div>
                `;
                log(`User authenticated: ${user.email}`, 'success');
            } else {
                userInfoDiv.innerHTML = '<p class="text-red-600">No user signed in</p>';
                log('No user authenticated', 'error');
            }
        }

        // Update user document info
        async function updateUserDocument(user) {
            const userDocDiv = document.getElementById('user-document');
            if (!user) {
                userDocDiv.innerHTML = '<p class="text-red-600">No user to check document for</p>';
                return;
            }

            try {
                const userDoc = await db.collection('users').doc(user.uid).get();
                if (userDoc.exists) {
                    const userData = userDoc.data();
                    userDocDiv.innerHTML = `
                        <div class="space-y-2">
                            <p><strong>Document exists:</strong> <span class="text-green-600">Yes</span></p>
                            <p><strong>Credits:</strong> ${userData.credits || 0}</p>
                            <p><strong>Email:</strong> ${userData.email}</p>
                            <p><strong>Created:</strong> ${userData.createdAt ? userData.createdAt.toDate().toLocaleString() : 'Unknown'}</p>
                            <div class="mt-4">
                                <strong>Full Document:</strong>
                                <pre class="bg-gray-100 p-2 rounded mt-2 text-xs overflow-x-auto">${JSON.stringify(userData, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                    log(`User document found with ${userData.credits || 0} credits`, 'success');
                } else {
                    userDocDiv.innerHTML = '<p class="text-red-600">User document does not exist in Firestore</p>';
                    log('User document not found in Firestore', 'error');
                }
            } catch (error) {
                userDocDiv.innerHTML = `<p class="text-red-600">Error loading document: ${error.message}</p>`;
                log(`Error loading user document: ${error.message}`, 'error');
            }
        }

        // Auth state observer
        auth.onAuthStateChanged(async (user) => {
            currentUser = user;
            updateUserInfo(user);
            await updateUserDocument(user);
        });

        // Initialize
        updateFirebaseStatus();

        // Button event listeners
        document.getElementById('create-user-doc').addEventListener('click', async () => {
            if (!currentUser) {
                log('No user signed in to create document for', 'error');
                return;
            }

            try {
                await db.collection('users').doc(currentUser.uid).set({
                    email: currentUser.email,
                    credits: 3,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    requests: []
                });
                log('User document created successfully with 3 credits', 'success');
                await updateUserDocument(currentUser);
            } catch (error) {
                log(`Error creating user document: ${error.message}`, 'error');
            }
        });

        document.getElementById('refresh-data').addEventListener('click', async () => {
            log('Refreshing all data...', 'info');
            updateUserInfo(currentUser);
            await updateUserDocument(currentUser);
        });

        document.getElementById('test-firestore').addEventListener('click', async () => {
            try {
                const testDoc = await db.collection('test').add({
                    message: 'Test write',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
                log(`Test write successful. Document ID: ${testDoc.id}`, 'success');
                
                // Clean up test document
                await testDoc.delete();
                log('Test document cleaned up', 'info');
            } catch (error) {
                log(`Test write failed: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
