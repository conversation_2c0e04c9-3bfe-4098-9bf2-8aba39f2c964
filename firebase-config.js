// Firebase Configuration
const firebaseConfig = {
    apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
    authDomain: "aifrobeats-com.firebaseapp.com",
    projectId: "aifrobeats-com",
    storageBucket: "aifrobeats-com.firebasestorage.app",
    messagingSenderId: "1013326896271",
    appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
    measurementId: "G-YCQLRG8L44"
};

// Initialize Firebase
import { initializeApp } from 'firebase/app';
import { getAuth, onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { getFirestore, doc, setDoc, getDoc, collection, addDoc, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Auth state management
export const initAuth = () => {
    return new Promise((resolve) => {
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                // User is signed in
                console.log('User signed in:', user.email);
                
                // Check if user document exists, create if not
                const userDocRef = doc(db, 'users', user.uid);
                const userDoc = await getDoc(userDocRef);
                
                if (!userDoc.exists()) {
                    // Create new user document with 3 free credits
                    await setDoc(userDocRef, {
                        email: user.email,
                        credits: 3,
                        createdAt: new Date(),
                        requests: []
                    });
                    console.log('New user document created with 3 credits');
                }
                
                resolve(user);
            } else {
                // User is signed out
                console.log('User signed out');
                resolve(null);
            }
        });
    });
};

// Authentication functions
export const signUp = async (email, password) => {
    try {
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);
        return { success: true, user: userCredential.user };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

export const signIn = async (email, password) => {
    try {
        const userCredential = await signInWithEmailAndPassword(auth, email, password);
        return { success: true, user: userCredential.user };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

export const logOut = async () => {
    try {
        await signOut(auth);
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

// User data functions
export const getUserData = async (userId) => {
    try {
        const userDocRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
            return { success: true, data: userDoc.data() };
        } else {
            return { success: false, error: 'User not found' };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
};

// Request functions
export const createRequest = async (userId, requestData) => {
    try {
        // First check if user has credits
        const userData = await getUserData(userId);
        if (!userData.success || userData.data.credits < 1) {
            return { success: false, error: 'Insufficient credits' };
        }
        
        // Create the request
        const requestRef = await addDoc(collection(db, 'requests'), {
            userId: userId,
            ...requestData,
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
        });
        
        // Deduct credit from user
        const userDocRef = doc(db, 'users', userId);
        await setDoc(userDocRef, {
            credits: userData.data.credits - 1
        }, { merge: true });
        
        return { success: true, requestId: requestRef.id };
    } catch (error) {
        return { success: false, error: error.message };
    }
};

// Get user requests
export const getUserRequests = (userId, callback) => {
    const q = query(
        collection(db, 'requests'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
    );
    
    return onSnapshot(q, (querySnapshot) => {
        const requests = [];
        querySnapshot.forEach((doc) => {
            requests.push({ id: doc.id, ...doc.data() });
        });
        callback(requests);
    });
};

// Admin functions (for admin dashboard)
export const getAllRequests = (callback) => {
    const q = query(
        collection(db, 'requests'),
        orderBy('createdAt', 'desc')
    );
    
    return onSnapshot(q, (querySnapshot) => {
        const requests = [];
        querySnapshot.forEach((doc) => {
            requests.push({ id: doc.id, ...doc.data() });
        });
        callback(requests);
    });
};

// Update request status (admin function)
export const updateRequestStatus = async (requestId, status, previewUrl = null) => {
    try {
        const requestRef = doc(db, 'requests', requestId);
        const updateData = {
            status: status,
            updatedAt: new Date()
        };
        
        if (previewUrl) {
            updateData.previewUrl = previewUrl;
        }
        
        await setDoc(requestRef, updateData, { merge: true });
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
};
