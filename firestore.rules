rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow creation of new user documents during signup
      allow create: if request.auth != null && request.auth.uid == userId
        && resource == null
        && request.resource.data.keys().hasAll(['email', 'credits', 'createdAt'])
        && request.resource.data.email == request.auth.token.email
        && request.resource.data.credits == 3;
    }
    
    // Requests collection
    match /requests/{requestId} {
      // Users can read their own requests
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      
      // Users can create requests if they have credits
      allow create: if request.auth != null 
        && request.resource.data.userId == request.auth.uid
        && request.resource.data.keys().hasAll(['userId', 'songTitle', 'description', 'status', 'createdAt'])
        && request.resource.data.status == 'pending';
      
      // Only admins can update requests (for status changes and adding preview URLs)
      // For now, we'll allow updates from authenticated users (you can restrict this later)
      allow update: if request.auth != null;
      
      // Admins can read all requests (you'll need to implement admin role checking)
      allow read: if request.auth != null;
    }
    
    // Admin collection (for admin users - implement role-based access later)
    match /admin/{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
