<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">

    <style>
        /* Custom form validation styling */
        .form-field-error {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3) !important;
        }

        .form-field-success {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
        }

        /* Enhanced form animations */
        .login-form-container {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    

</head>
<body class="min-h-screen flex flex-col" style="background: linear-gradient(135deg, var(--hero-gradient-start), var(--hero-gradient-end));">
    
    <!-- Navigation Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-10 backdrop-blur-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-white hover:text-green-400 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="signup.html" class="primary-button">
                    <i class="fas fa-user-plus mr-2"></i>Sign Up
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content Container -->
    <main class="flex-1 flex items-center justify-center px-6 py-24">
        <!-- Login Container -->
        <div class="w-full max-w-md">
            <div class="login-form-container bg-white bg-opacity-10 backdrop-blur-md rounded-2xl shadow-2xl p-8 border border-white border-opacity-20">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-music text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-white mb-2">Welcome Back!</h1>
                <p class="text-gray-300">Sign in to access your music dashboard</p>
            </div>

            <!-- Benefits Reminder -->
            <div class="bg-green-500 bg-opacity-20 rounded-lg p-4 mb-6 border border-green-400 border-opacity-30">
                <h3 class="text-green-300 font-semibold mb-2 flex items-center">
                    <i class="fas fa-gift mr-2"></i>Your Account Benefits
                </h3>
                <ul class="text-sm text-green-200 space-y-1">
                    <li>• 3 Free Preview Credits</li>
                    <li>• Track Your Song Requests</li>
                    <li>• Download Your Custom Music</li>
                    <li>• Priority Customer Support</li>
                </ul>
            </div>

            <!-- Custom Login Form -->
            <form id="login-form" class="space-y-4">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                    <input type="email" id="email" name="email" required 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your email">
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                    <input type="password" id="password" name="password" required 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Enter your password">
                </div>
                <button type="submit" id="login-btn" class="w-full primary-button py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                    <i id="login-icon" class="fas fa-sign-in-alt mr-2"></i>
                    <span id="login-text">Sign In</span>
                </button>
            </form>

            <!-- Error Message -->
            <div id="error-message" class="hidden bg-red-500 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg p-3 mb-4">
                <p class="text-red-300 text-sm"></p>
            </div>

            <!-- Success Message -->
            <div id="success-message" class="hidden bg-green-500 bg-opacity-20 border border-green-400 border-opacity-30 rounded-lg p-3 mb-4">
                <p class="text-green-300 text-sm"></p>
            </div>

            <!-- Footer Links -->
            <div class="text-center space-y-3">
                <p class="text-gray-400 text-sm">
                    Don't have an account?
                    <a href="signup.html" class="text-green-400 hover:text-green-300 font-semibold">Sign up here</a>
                </p>
                <p class="text-gray-400 text-sm">
                    Forgot your password?
                    <button id="reset-password-btn" class="text-green-400 hover:text-green-300 font-semibold underline">Reset it here</button>
                </p>
                <p class="text-gray-500 text-xs">
                    By signing in, you agree to our
                    <a href="terms.html" class="underline hover:text-green-400">Terms of Service</a> and
                    <a href="privacy.html" class="underline hover:text-green-400">Privacy Policy</a>
                </p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-400 py-6">
        <div class="container mx-auto px-6 text-center">
            <div class="flex items-center justify-center mb-4">
                <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-6 h-6 mr-2">
                <span class="animated-gradient-text text-lg font-semibold">Aifrobeats</span>
            </div>
            <p class="text-sm mb-4">AI-Generated Afrobeats, Human-Curated for you.</p>
            <div class="flex justify-center space-x-4 text-xs">
                <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                <span>|</span>
                <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                <span>|</span>
                <a href="index.html" class="hover:text-green-400 transition-colors">Back to Home</a>
            </div>
            <p class="text-xs mt-4">&copy; <span id="currentYear"></span> AIFROBEATS - All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Password Reset Modal -->
    <div id="reset-password-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Reset Password</h3>
                <button id="close-reset-modal" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <p class="text-yellow-800 text-sm">
                    <i class="fas fa-info-circle mr-2"></i>
                    This email already exists without any means of sign-in. Please reset the password to recover.
                </p>
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Get instructions sent to this email that explain how to reset your password
            </p>
            <div class="mb-4">
                <label for="reset-email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input type="email" id="reset-email"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                       placeholder="Enter your email address">
            </div>
            <div class="flex space-x-3">
                <button id="cancel-reset" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button id="send-reset" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Send
                </button>
            </div>
            <div class="mt-4 text-xs text-gray-500 space-x-2">
                <a href="terms.html" class="hover:text-green-600">Terms of Service</a>
                <a href="privacy.html" class="hover:text-green-600">Privacy Policy</a>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Signing you in...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>


    <!-- Login Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Custom login workflow - NO Firebase UI
        let isLoggingIn = false;

        // Custom login form handler
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            if (isLoggingIn) {
                console.log('Already logging in, ignoring duplicate submission');
                return;
            }

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            console.log('Login attempt:', { email, passwordLength: password.length });

            // Validation
            if (!email || !password) {
                showError('Please enter both email and password.');
                return;
            }

            if (!email.includes('@') || !email.includes('.')) {
                showError('Please enter a valid email address.');
                return;
            }

            // Start login process
            isLoggingIn = true;
            setLoginButtonState('loading');
            showLoading(true);

            try {
                console.log('Step 1: Signing in with email and password...');

                // Step 1: Sign in user
                const userCredential = await auth.signInWithEmailAndPassword(email, password);
                const user = userCredential.user;

                console.log('Step 1 SUCCESS: User signed in:', user.uid, user.email);

                // Step 2: Check/create user document
                console.log('Step 2: Checking user document...');

                try {
                    const userDoc = await db.collection('users').doc(user.uid).get();
                    if (!userDoc.exists) {
                        console.log('User document missing, creating...');
                        await db.collection('users').doc(user.uid).set({
                            email: user.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                            requests: [],
                            profile: {
                                displayName: user.displayName || '',
                                photoURL: user.photoURL || '',
                                emailVerified: user.emailVerified
                            },
                            metadata: {
                                creationTime: user.metadata.creationTime,
                                lastSignInTime: user.metadata.lastSignInTime
                            }
                        });
                        console.log('Step 2 SUCCESS: User document created');
                    } else {
                        console.log('Step 2 SUCCESS: User document exists');
                    }
                } catch (firestoreError) {
                    console.error('Firestore error (non-critical):', firestoreError);
                    // Continue even if Firestore fails
                }

                // Step 3: Success - redirect to dashboard
                console.log('Step 3: Login complete, redirecting...');
                showLoading(false);
                setLoginButtonState('success');
                showSuccess('Welcome back! Redirecting to your dashboard...');

                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);

            } catch (error) {
                console.error('Login error:', error);
                isLoggingIn = false;
                showLoading(false);
                setLoginButtonState('error');

                // Handle specific errors
                if (error.code === 'auth/user-not-found') {
                    showError('No account found with this email. Please sign up first or check your email address.');
                } else if (error.code === 'auth/wrong-password') {
                    showError('Incorrect password. Please try again or reset your password.');
                } else if (error.code === 'auth/invalid-email') {
                    showError('Please enter a valid email address.');
                } else if (error.code === 'auth/user-disabled') {
                    showError('This account has been disabled. Please contact support.');
                } else if (error.code === 'auth/too-many-requests') {
                    showError('Too many failed login attempts. Please try again later or reset your password.');
                } else if (error.code === 'auth/network-request-failed') {
                    showError('Network error. Please check your internet connection and try again.');
                } else if (error.message && error.message.includes('already exists without any means of sign-in')) {
                    // Show the reset password modal for this specific case
                    const email = error.email || document.getElementById('email').value;
                    document.getElementById('reset-email').value = email;
                    document.getElementById('reset-password-modal').classList.remove('hidden');
                    showError('This email exists but needs password reset. Please use the reset form that just opened.');
                } else {
                    showError(`Login failed: ${error.message || 'Please try again.'}`);
                }

                // Reset button after error
                setTimeout(() => {
                    setLoginButtonState('default');
                }, 3000);
            }
        });

        // Check if user is already signed in
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                console.log('User already signed in:', user.uid);

                // Check if user document exists and create if missing
                try {
                    const userDoc = await db.collection('users').doc(user.uid).get();
                    if (!userDoc.exists) {
                        console.log('User document missing, creating...');
                        await db.collection('users').doc(user.uid).set({
                            email: user.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                            requests: [],
                            profile: {
                                displayName: user.displayName || '',
                                photoURL: user.photoURL || ''
                            }
                        });
                        console.log('User document created for existing user');
                    }
                } catch (error) {
                    console.error('Error checking/creating user document:', error);
                }

                // User is signed in, redirect to dashboard
                showSuccess('Welcome back! Redirecting to your dashboard...');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } else {
                console.log('No user signed in, showing login form');
            }
        });

        // Button state management
        function setLoginButtonState(state) {
            const button = document.getElementById('login-btn');
            const icon = document.getElementById('login-icon');
            const text = document.getElementById('login-text');

            switch (state) {
                case 'loading':
                    button.disabled = true;
                    icon.className = 'fas fa-spinner fa-spin mr-2';
                    text.textContent = 'Signing In...';
                    break;
                case 'success':
                    button.disabled = true;
                    icon.className = 'fas fa-check mr-2';
                    text.textContent = 'Welcome Back!';
                    break;
                case 'error':
                    button.disabled = true;
                    icon.className = 'fas fa-exclamation-triangle mr-2';
                    text.textContent = 'Login Failed';
                    break;
                case 'default':
                default:
                    button.disabled = false;
                    icon.className = 'fas fa-sign-in-alt mr-2';
                    text.textContent = 'Sign In';
                    break;
            }
        }

        // Utility functions
        function showError(message) {
            console.error('Error:', message);
            const errorDiv = document.getElementById('error-message');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');

            // Hide success message if showing
            document.getElementById('success-message').classList.add('hidden');

            setTimeout(() => errorDiv.classList.add('hidden'), 8000);
        }

        function showSuccess(message) {
            console.log('Success:', message);
            const successDiv = document.getElementById('success-message');
            successDiv.querySelector('p').textContent = message;
            successDiv.classList.remove('hidden');

            // Hide error message if showing
            document.getElementById('error-message').classList.add('hidden');

            setTimeout(() => successDiv.classList.add('hidden'), 8000);
        }

        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        // Password Reset Modal functionality
        const resetPasswordBtn = document.getElementById('reset-password-btn');
        const resetPasswordModal = document.getElementById('reset-password-modal');
        const closeResetModal = document.getElementById('close-reset-modal');
        const cancelReset = document.getElementById('cancel-reset');
        const sendReset = document.getElementById('send-reset');
        const resetEmailInput = document.getElementById('reset-email');

        // Show reset password modal
        resetPasswordBtn.addEventListener('click', () => {
            resetPasswordModal.classList.remove('hidden');
        });

        // Hide reset password modal
        function hideResetModal() {
            resetPasswordModal.classList.add('hidden');
            resetEmailInput.value = '';
        }

        closeResetModal.addEventListener('click', hideResetModal);
        cancelReset.addEventListener('click', hideResetModal);

        // Send password reset email
        sendReset.addEventListener('click', async () => {
            const email = resetEmailInput.value.trim();

            if (!email) {
                showError('Please enter your email address.');
                return;
            }

            if (!email.includes('@')) {
                showError('Please enter a valid email address.');
                return;
            }

            try {
                showLoading(true);
                await auth.sendPasswordResetEmail(email);
                hideResetModal();
                showSuccess('Password reset email sent! Check your inbox and follow the instructions.');
            } catch (error) {
                console.error('Password reset error:', error);

                if (error.code === 'auth/user-not-found') {
                    showError('No account found with this email address.');
                } else if (error.code === 'auth/invalid-email') {
                    showError('Please enter a valid email address.');
                } else if (error.code === 'auth/too-many-requests') {
                    showError('Too many requests. Please try again later.');
                } else {
                    showError('Failed to send reset email. Please try again.');
                }
            } finally {
                showLoading(false);
            }
        });

        // Handle Enter key in reset email input
        resetEmailInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendReset.click();
            }
        });

        // Real-time form validation
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');

        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function updateFieldValidation(input, isValid) {
            input.classList.remove('border-red-500', 'border-green-500');
            if (input.value.length > 0) {
                input.classList.add(isValid ? 'border-green-500' : 'border-red-500');
            }
        }

        // Email validation
        emailInput.addEventListener('input', () => {
            const isValid = validateEmail(emailInput.value);
            updateFieldValidation(emailInput, isValid);
        });

        // Password validation (just check if not empty)
        passwordInput.addEventListener('input', () => {
            const isValid = passwordInput.value.length > 0;
            updateFieldValidation(passwordInput, isValid);
        });

        // Handle Enter key submission
        document.getElementById('login-form').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const email = emailInput.value.trim();
                const password = passwordInput.value;

                if (!validateEmail(email) || password.length === 0) {
                    e.preventDefault();
                    showError('Please enter a valid email and password.');
                }
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
