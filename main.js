document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations
    AOS.init({
        duration: 800,
        once: true
    });

    // Timeline functionality
    const steps = document.querySelectorAll('.timeline-step');
    const content = document.getElementById('timelineContent');
    const timelineProgress = document.getElementById('timelineProgress');

    if (steps.length > 0 && content && timelineProgress) {
        let currentStep = 0;

        const stepContents = [
            {
                title: "Share Your Vision",
                text: "Tell us about your song idea, occasion, names, and the vibe you want. The more details you provide, the better your results!"
            },
            {
                title: "AI Music Generation",
                text: "Our advanced AI technology creates multiple high-quality options based on your specifications."
            },
            {
                title: "Expert Producer Selection",
                text: "Our professional producers review the AI-generated tracks and select the perfect one that matches your brief."
            },
            {
                title: "Your Custom Track Delivered",
                text: "Receive your professionally produced, custom Afrobeats track in just 1-3 hours!"
            }
        ];

        function updateTimeline() {
            // First fade out the content
            content.style.opacity = '0';

            // After content fades out, update the active step and content
            setTimeout(() => {
                steps.forEach((step, index) => {
                    const stepCircle = step.querySelector('div[role="tab"]');
                    const stepHeading = step.querySelector('h4');

                    if (index === currentStep) {
                        // Update active step
                        step.classList.add('active');
                        stepCircle.style.backgroundColor = 'var(--timeline-step-active-bg)';
                        stepCircle.classList.add('shadow-lg');
                        stepCircle.classList.remove('shadow-md');
                        stepCircle.setAttribute('aria-selected', 'true');
                        stepCircle.style.transform = 'scale(1.1)';

                        // Update text colors
                        stepHeading.style.color = 'var(--timeline-text-active)';

                        // Calculate precise positions for the slider knob
                        const timelineContainer = document.querySelector('.relative.overflow-hidden');
                        const timelineWidth = timelineContainer.offsetWidth - 16; // Account for margins
                        const stepElements = document.querySelectorAll('.timeline-step div[role="tab"]');

                        // Get the positions of each step circle's center
                        const stepPositions = Array.from(stepElements).map(el => {
                            const rect = el.getBoundingClientRect();
                            const containerRect = timelineContainer.getBoundingClientRect();
                            // Calculate position relative to the timeline container
                            return (rect.left + rect.width / 2) - containerRect.left;
                        });

                        // Set the width based on the current step's position
                        if (index === 0) {
                            // First step - position at the middle of the first number
                            timelineProgress.style.width = `${stepPositions[0]}px`;
                        } else if (index === steps.length - 1) {
                            // Last step - position at the end of the timeline
                            timelineProgress.style.width = `${timelineWidth}px`;
                        } else {
                            // Middle steps - position at the middle of the corresponding number
                            timelineProgress.style.width = `${stepPositions[index]}px`;
                        }

                        // Update content
                        content.innerHTML = `
                            <h4 class="text-xl font-bold mb-3" style="color: var(--timeline-text-active);">${stepContents[index].title}</h4>
                            <p>${stepContents[index].text}</p>
                        `;
                    } else {
                        // Reset inactive steps
                        step.classList.remove('active');
                        stepCircle.style.backgroundColor = 'var(--timeline-step-inactive-bg)';
                        stepCircle.classList.add('shadow-md');
                        stepCircle.classList.remove('shadow-lg');
                        stepCircle.setAttribute('aria-selected', 'false');
                        stepCircle.style.transform = 'scale(1)';

                        // Reset text colors
                        stepHeading.style.color = 'var(--timeline-text-inactive)';
                    }
                });

                // Fade the content back in
                setTimeout(() => {
                    content.style.opacity = '1';
                }, 100);

                // Move to next step
                currentStep = (currentStep + 1) % steps.length;
            }, 300); // This timeout should match the fade-out duration
        }

        // Set initial state
        steps.forEach((step, index) => {
            const stepCircle = step.querySelector('div[role="tab"]');
            const stepHeading = step.querySelector('h4');
            if (index === 0) {
                stepCircle.style.backgroundColor = 'var(--timeline-step-active-bg)';
                stepCircle.classList.add('shadow-lg');
                stepCircle.style.transform = 'scale(1.1)';
                stepCircle.setAttribute('aria-selected', 'true');
                stepHeading.style.color = 'var(--timeline-text-active)';

                // Position the progress bar at the middle of the first number
                setTimeout(() => {
                    const timelineContainer = document.querySelector('.relative.overflow-hidden');
                    const firstStep = document.querySelector('.timeline-step:first-child div[role="tab"]');
                    if (timelineContainer && firstStep) {
                        const rect = firstStep.getBoundingClientRect();
                        const containerRect = timelineContainer.getBoundingClientRect();
                        const initialPosition = (rect.left + rect.width / 2) - containerRect.left;
                        timelineProgress.style.width = `${initialPosition}px`;
                    }
                }, 100);
            } else {
                stepCircle.style.backgroundColor = 'var(--timeline-step-inactive-bg)';
                stepCircle.setAttribute('aria-selected', 'false');
                stepHeading.style.color = 'var(--timeline-text-inactive)';
            }
        });

        // Initial update after a short delay
        setTimeout(updateTimeline, 1000);

        // Set interval for animation
        const animationInterval = setInterval(updateTimeline, 4000);

        // Add hover pause functionality
        const timelineContainer = document.querySelector('.production-timeline');
        let currentInterval = animationInterval;

        timelineContainer.addEventListener('mouseenter', () => {
            clearInterval(currentInterval);
        });

        timelineContainer.addEventListener('mouseleave', () => {
            currentInterval = setInterval(updateTimeline, 4000);
        });
    }

    // Ensure video background plays correctly on desktop only
    const heroVideo = document.querySelector('.hero-video');
    // Check if we're not on mobile before loading video
    const isMobile = window.matchMedia('(max-width: 768px)').matches;

    if (heroVideo && !isMobile) {
        // Force video to play if it's not already playing (desktop only)
        heroVideo.play().catch(error => {
            console.log('Auto-play was prevented:', error);
            // Add a play button for browsers that block autoplay
            if (!document.getElementById('video-play-btn')) {
                const playBtn = document.createElement('button');
                playBtn.id = 'video-play-btn';
                playBtn.innerHTML = '<i class="fas fa-play"></i>';
                playBtn.style.cssText = 'position:absolute; z-index:5; top:50%; left:50%; transform:translate(-50%,-50%); background:rgba(0,0,0,0.5); color:white; border:none; border-radius:50%; width:60px; height:60px; cursor:pointer;';
                heroVideo.parentNode.insertBefore(playBtn, heroVideo.nextSibling);

                playBtn.addEventListener('click', () => {
                    heroVideo.play();
                    playBtn.style.display = 'none';
                });
            }
        });

        // Handle visibility change to pause/play video when tab is inactive/active
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                heroVideo.pause();
            } else {
                heroVideo.play().catch(e => console.log('Could not auto-resume video:', e));
            }
        });
    }

    // Function to select song type in the dropdown when clicking on use case cards
    window.selectSongType = function(songType) {
        // Set a timeout to ensure the page has scrolled to the form section before selecting
        setTimeout(() => {
            const songTypeSelect = document.getElementById('song-type');
            if (songTypeSelect) {
                songTypeSelect.value = songType;
                // Add a highlight effect to the dropdown
                songTypeSelect.classList.add('ring-2', 'ring-green-500');
                setTimeout(() => {
                    songTypeSelect.classList.remove('ring-2', 'ring-green-500');
                }, 1500);
            }
        }, 500);
    }

    // Theme toggle functionality
    const themeToggle = document.getElementById('themeToggle');
    const mobileThemeToggle = document.getElementById('mobileThemeToggle');
    const stickyThemeToggle = document.getElementById('stickyThemeToggle');

    // Check for saved theme preference or use device preference
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    // Set initial theme
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.documentElement.setAttribute('data-theme', 'dark');
        updateThemeIcons('dark');
    } else {
        document.documentElement.setAttribute('data-theme', 'light');
        updateThemeIcons('light');
    }

    // Function to toggle theme
    function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        updateThemeIcons(newTheme);
    }

    // Update all theme toggle icons
    function updateThemeIcons(theme) {
        const moonIcon = '<i class="fas fa-moon"></i>';
        const sunIcon = '<i class="fas fa-sun"></i>';

        if (themeToggle) themeToggle.innerHTML = theme === 'light' ? moonIcon : sunIcon;
        if (mobileThemeToggle) mobileThemeToggle.innerHTML = theme === 'light' ? moonIcon : sunIcon;
        if (stickyThemeToggle) stickyThemeToggle.innerHTML = theme === 'light' ? moonIcon : sunIcon;
    }

    // Add event listeners to theme toggles
    if (themeToggle) themeToggle.addEventListener('click', toggleTheme);
    if (mobileThemeToggle) mobileThemeToggle.addEventListener('click', toggleTheme);
    if (stickyThemeToggle) stickyThemeToggle.addEventListener('click', toggleTheme);

    // Mobile menu toggle - main header
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            mobileMenu.classList.toggle('open');
            mobileMenuButton.classList.toggle('active');
        });
    }

    // We've removed the sticky mobile menu toggle since we now only have a button

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            // Check if target is just "#" or a valid ID
            if (targetId.length > 1 && document.querySelector(targetId)) {
                document.querySelector(targetId).scrollIntoView({
                    behavior: 'smooth'
                });
                // Close mobile menus on click
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.classList.remove('open');
                    if (mobileMenuButton) mobileMenuButton.classList.remove('active');
                }

                // We've removed the sticky mobile menu
            } else if (targetId === '#') { // Scroll to top if href is just "#"
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    });

    // Set current year in footer
    const currentYearElement = document.getElementById('currentYear');
    if (currentYearElement) {
        currentYearElement.textContent = new Date().getFullYear();
    }

    // Sticky navigation
    const stickyNav = document.getElementById('sticky-nav');
    if (stickyNav) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                stickyNav.classList.add('visible');

                // Close the main mobile menu when sticky nav appears
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.classList.remove('open');
                    if (mobileMenuButton) mobileMenuButton.classList.remove('active');
                }
            } else {
                stickyNav.classList.remove('visible');

                // We've removed the sticky mobile menu
            }
        });
    }

    // FAQ accordion
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
        const header = item.querySelector('.faq-header');
        header.addEventListener('click', () => {
            // Close all other items
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
            // Toggle current item
            item.classList.toggle('active');
        });
    });

    // Initialize EmailJS
    (function() {
        // TODO: Replace with your actual EmailJS public key
        // You need to sign up at https://www.emailjs.com/ and get your public key
        emailjs.init("u899JogrVD_YHNQIU");
    })();

    // Form validation function
    function validateForm() {
        const name = document.getElementById('name').value.trim();
        const email = document.getElementById('email').value.trim();
        const songType = document.getElementById('song-type').value;
        const songIdea = document.getElementById('song-idea').value.trim();

        if (!name) {
            alert('Please enter your name');
            return false;
        }

        if (!email) {
            alert('Please enter your email');
            return false;
        }

        if (!validateEmail(email)) {
            alert('Please enter a valid email address');
            return false;
        }

        if (!songType) {
            alert('Please select a song type');
            return false;
        }

        if (!songIdea || songIdea.length < 50) {
            alert('Please provide a detailed song idea (minimum 50 characters)');
            return false;
        }

        return true;
    }

    // Email validation function
    function validateEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    // Form submission with EmailJS
    const orderForm = document.getElementById('orderForm');
    if(orderForm) {
        orderForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Validate form
            if (!validateForm()) {
                return;
            }

            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            submitButton.disabled = true;

            // Show loading overlay
            document.getElementById('loadingOverlay').classList.remove('hidden');

            // Collect form data
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                whatsapp: document.getElementById('whatsapp').value,
                telegram: document.getElementById('telegram').value,
                songType: document.getElementById('song-type').value,
                songIdea: document.getElementById('song-idea').value
            };

            // Send the form data to EmailJS
            // TODO: Replace with your actual EmailJS service ID and template ID
            // You need to create a service and template in your EmailJS dashboard
            // The template should have variables matching the formData object keys
            emailjs.send("service_4vh02fv", "template_fnabhb3", formData)
                .then(function(response) {
                    console.log("SUCCESS!", response.status, response.text);

                    // Redirect to Gumroad product page
                    window.location.href = "https://aifrobeats.gumroad.com/l/gpgqic?_gl=1*v2cv6z*_ga*NzcxODcyMzc0LjE3NDc0NDg2NDU.*_ga_6LJN6D94N6*czE3NDc0NDg2NDUkbzEkZzEkdDE3NDc0NTEwODgkajAkbDAkaDA";

                }, function(error) {
                    console.log("FAILED...", error);

                    // Reset button state
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;

                    // Hide loading overlay
                    document.getElementById('loadingOverlay').classList.add('hidden');

                    // Show error message
                    alert("There was an error sending your form. Please try again or contact us directly.");
                });
        });
    }
});
