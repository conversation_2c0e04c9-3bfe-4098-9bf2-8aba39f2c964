<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ditch Studio Fees, Get Your Jingles and Songs for Cheap!</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/rebound/0.1.0/rebound.min.js"></script>
    <style>
        .fixed-bg {
            background-image: url('images/background.jpg');
            background-attachment: fixed;
            background-size: cover;
            background-position: center;
        }
        :root {
            /* Light theme (default) */
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-card: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
            --text-muted: #6b7280;
            --border-color: rgba(0, 0, 0, 0.05);
            --border-color-rgb: 0, 0, 0;
            --shadow-color: rgba(0, 0, 0, 0.05);
            --shadow-color-hover: rgba(0, 0, 0, 0.1);
            --accent-color: #22c55e;
            --accent-color-dark: #16a34a;
            --header-bg: rgba(255, 255, 255, 0.95);
            --player-bg: rgba(255, 255, 255, 0.95);
            --hero-gradient-start: #111827;
            --hero-gradient-end: #1f2937;
            --footer-bg: #111827;
            --footer-text: #9ca3af;
            --card-hover-transform: translateY(-5px);
            --playlist-bg: rgba(255, 255, 255, 0.95);
            --playlist-hover: rgba(0, 0, 0, 0.05);
            --playlist-divider: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            /* Dark theme */
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --bg-card: #1f2937;
            --text-primary: #ffffff;
            --text-secondary: #f3f4f6;
            --text-muted: #d1d5db;
            --border-color: rgba(255, 255, 255, 0.1);
            --border-color-rgb: 255, 255, 255;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --shadow-color-hover: rgba(0, 0, 0, 0.4);
            --accent-color: #22c55e;
            --accent-color-dark: #16a34a;
            --header-bg: rgba(17, 24, 39, 0.95);
            --player-bg: rgba(17, 24, 39, 0.95);
            --hero-gradient-start: rgba(17, 24, 39, 0.85);
            --hero-gradient-end: rgba(31, 41, 55, 0.85);
            --footer-bg: #0f172a;
            --footer-text: #d1d5db;
            --card-hover-transform: translateY(-5px);
            --playlist-bg: rgba(17, 24, 39, 0.95);
            --playlist-hover: rgba(255, 255, 255, 0.1);
            --playlist-divider: rgba(255, 255, 255, 0.1);
        }

        /* Shared styles from index.html */
        .primary-button {
            background: linear-gradient(45deg, var(--accent-color), var(--accent-color-dark));
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.7);
            animation: pulse 2s infinite;
            position: relative;
            overflow: hidden;
            z-index: 5;
        }

        .primary-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 15px 2px rgba(255, 165, 0, 0.9);
        }

        .primary-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
        }

        .primary-button:hover::before {
            left: 100%;
        }

        .secondary-button {
            background-color: var(--bg-secondary);
            color: var(--text-secondary);
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .secondary-button:hover {
            background-color: var(--bg-primary);
            color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .theme-text {
            color: var(--text-primary);
            transition: color 0.3s ease;
        }

        .theme-text-muted {
            color: var(--text-muted);
            transition: color 0.3s ease;
        }

        .theme-bg {
            background-color: var(--bg-card);
            transition: background-color 0.3s ease;
        }

        .card {
            background-color: var(--bg-card);
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .card:hover {
            box-shadow: 0 10px 15px var(--shadow-color-hover);
            transform: var(--card-hover-transform);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1.25rem;
            transition: transform 0.3s ease;
        }

        .card:hover .feature-icon {
            transform: scale(1.1);
        }

        /* Fixed Music Player Styles */
        .music-player-container {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: var(--player-bg);
            box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
            z-index: 40;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
        }

        .music-player-container.active {
            transform: translateY(0);
        }

        .music-player {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .music-player-album {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 0.5rem;
            overflow: visible;
            margin-right: 1rem;
            flex-shrink: 0;
            position: relative;
        }

        .music-player-album img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 0.5rem;
        }

        /* Vinyl Record Styles */
        .vinyl-container {
            position: relative;
        }

        .vinyl-record {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 2;
            transform: scale(1.5);
            transform-origin: center;
            opacity: 0.85;
            transition: transform 0.5s ease;
            filter: drop-shadow(0 2px 4px var(--shadow-color));
            pointer-events: none; /* Allow clicks to pass through to the album art */
        }

        .vinyl-animation {
            animation: rotation 3s infinite linear;
        }

        @keyframes rotation {
            from {
                transform: scale(1.5) rotate(0deg);
            }
            to {
                transform: scale(1.5) rotate(359deg);
            }
        }

        .music-player-info {
            flex: 1;
            margin-right: 1.5rem;
            min-width: 0;
        }

        .music-player-title {
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-player-artist {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-player-controls {
            display: flex;
            align-items: center;
            margin-right: 1.5rem;
        }

        .music-player-control {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
            transition: color 0.3s ease;
        }

        .music-player-control:hover {
            color: var(--accent-color);
        }

        .music-player-control.play-pause {
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            color: white;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .music-player-control.play-pause:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .music-player-progress-container {
            flex: 1;
            height: 4px;
            background-color: var(--bg-secondary);
            border-radius: 2px;
            cursor: pointer;
            margin-right: 1rem;
            position: relative;
        }

        .music-player-progress {
            height: 100%;
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            border-radius: 2px;
            width: 0;
            position: relative;
        }

        .music-player-progress-handle {
            width: 12px;
            height: 12px;
            background-color: var(--accent-color);
            border-radius: 50%;
            position: absolute;
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
            display: none;
        }

        .music-player-progress:hover .music-player-progress-handle {
            display: block;
        }

        .music-player-time {
            color: var(--text-muted);
            font-size: 0.75rem;
            margin-right: 1rem;
            white-space: nowrap;
        }

        .music-player-volume-container {
            display: flex;
            align-items: center;
        }

        .music-player-volume-icon {
            color: var(--text-secondary);
            margin-right: 0.5rem;
            cursor: pointer;
        }

        .music-player-volume-slider {
            width: 60px;
            height: 4px;
            background-color: var(--bg-secondary);
            border-radius: 2px;
            cursor: pointer;
            position: relative;
        }

        .music-player-volume-level {
            height: 100%;
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            border-radius: 2px;
            width: 70%;
        }

        .music-player-toggle {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            width: 3rem;
            height: 3rem;
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 50;
            transition: transform 0.3s ease;
        }

        .music-player-toggle:hover {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .music-player-info {
                display: none;
            }

            .music-player-time {
                display: none;
            }

            .music-player-volume-container {
                display: none;
            }

            /* Adjust vinyl record for mobile */
            .music-player-album {
                width: 3rem;
                height: 3rem;
                margin-right: 0.75rem;
            }

            .vinyl-record {
                transform: scale(1.4);
            }

            @keyframes rotation {
                from {
                    transform: scale(1.4) rotate(0deg);
                }
                to {
                    transform: scale(1.4) rotate(359deg);
                }
            }
        }

        body {
            font-family: 'Poppins', sans-serif;
            scroll-behavior: smooth;
            padding-bottom: 80px; /* Add padding to prevent content from being hidden behind the player */
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
            user-select: none;
            -moz-user-select: none;
            -khtml-user-select: none;
            -webkit-user-select: none;
            -o-user-select: none;
        }

        /* Player Extended Styles */
        .player-extended {
            width: 100%;
            height: 50vh;
            overflow: hidden;
            text-align: center;
            position: relative;
            background-color: var(--bg-secondary);
        }

        .bgBox {
            width: 100%;
            height: 100%;
            position: absolute;
            z-index: 1;
            left: 0;
            top: 0;
            background-size: cover;
            background-repeat: no-repeat;
            opacity: .1;
            background-position: center center;
        }

        .bgBox img {
            display: none;
        }

        .player-extended h1 {
            text-transform: uppercase;
            white-space: nowrap;
            color: var(--text-primary);
            font-weight: 700;
            position: absolute;
            font-size: 5vw;
            text-align: center;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
        }

        /* Main Player Styles */
        .main {
            position: relative;
            z-index: 3;
            margin: -100px auto 0;
            border: 10px solid rgba(0,0,0,0);
            max-width: 800px;
        }

        .player {
            background-color: var(--bg-card);
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        /* Playlist Styles */
        .playlist {
            background-color: var(--playlist-bg);
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .playlist ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        .playlist li {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .playlist li:hover {
            background-color: var(--playlist-hover);
        }

        .playlist li:after {
            content: '';
            display: block;
            height: 1px;
            background-color: var(--playlist-divider);
            margin-top: 10px;
        }

        .playlist li:last-child:after {
            display: none;
        }

        .playlist figure {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            overflow: hidden;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .playlist img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .playlist label {
            flex: 1;
        }

        .playlist strong, .playlist span {
            display: block;
        }

        .playlist span {
            color: var(--text-muted);
            font-size: 0.9em;
            margin-top: 4px;
        }

        .playlist time {
            color: var(--text-muted);
            font-size: 0.9em;
        }

        /* Current Track Styles */
        .currentTrack {
            position: relative;
            margin: 0;
            padding: 0;
        }

        .currentTrackCover {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
        }

        .currentTrack img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            opacity: 0.8;
        }

        .currentTrack figcaption {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 20px;
            color: white;
            z-index: 3;
            text-align: center;
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
        }

        /* Controls Styles */
        .controls {
            padding: 30px;
            position: relative;
        }

        .title {
            font-size: 24px;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .audio {
            position: relative;
            height: 160px;
            display: flex;
            flex-direction: column;
        }

        .audio-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            height: 80px;
            position: relative;
        }

        .play-pause {
            position: relative;
            width: 60px;
            height: 60px;
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            z-index: 2;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            margin: 0 auto 20px;
        }

        .play-pause .fa {
            font-size: 24px;
        }

        .play-pause.active {
            transform: scale(1.05);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .scrubber {
            position: relative;
            height: 4px;
            background-color: var(--bg-secondary);
            border-radius: 2px;
            width: 100%;
            margin-top: 10px;
        }

        .progress, .loaded {
            position: absolute;
            height: 100%;
            left: 0;
            top: 0;
        }

        .progress {
            background-color: var(--bg-secondary);
            width: 100%;
        }

        .loaded {
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            width: 0;
            z-index: 2;
        }

        .time {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            color: var(--text-muted);
            font-size: 0.9em;
        }

        .buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            color: var(--text-muted);
        }

        .buttons div {
            cursor: pointer;
            transition: color 0.3s ease;
            padding: 5px 10px;
            border-radius: 5px;
        }

        .buttons div:hover {
            color: var(--accent-color);
        }

        .buttons div.active {
            color: var(--accent-color);
        }

        .back-to-list {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Menu Button Styles */
        .menu-button {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(0,0,0,0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
        }

        .menu-button span {
            display: block;
            width: 20px;
            height: 2px;
            background-color: white;
            position: relative;
        }

        .menu-button span::before,
        .menu-button span::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: white;
            left: 0;
        }

        .menu-button span::before {
            top: -6px;
        }

        .menu-button span::after {
            bottom: -6px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .main {
                margin-top: -50px;
            }

            .player-extended {
                height: 30vh;
            }

            .currentTrack img {
                height: 200px;
            }
        }

        /* Turntable Player Styles */
        .turntable-player {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            position: relative;
            padding: 0 20px;
        }

        .now-playing-board {
            height: 70px;
            width: 300px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 10px;
            display: flex;
            justify-content: flex-end;
            background-color: var(--bg-secondary);
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .now-playing-board-bottom-bar {
            height: 50px;
            width: 300px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 10px;
            display: flex;
            justify-content: flex-end;
            background-color: var(--bg-secondary);
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        #now-playing-board-id {
            transform: translatey(56%);
            transition: transform 2s;
        }

        #now-playing-board-bottom-bar-id {
            transform: translatey(-20%);
            transition: transform 2s;
        }

        .now-playing-details {
            display: flex;
            flex-direction: column;
            color: var(--text-primary);
            padding-top: 2px;
            width: 100%;
            padding: 10px;
        }

        .now-playing-details > h4, h6, .slidecontainer-one {
            font-family: 'Poppins', sans-serif;
            width: 180px;
            margin: 0px;
            margin-top: 5px;
            color: var(--text-primary);
            opacity: 1;
        }

        /* seek bar style */
        .slidecontainer-one {
            width: 90%;
            position: relative;
        }

        #completionBar {
            position: relative;
            top: 0;
            width: 0%;
            height: 5px;
            background: var(--accent-color);
            z-index: 0;
        }

        .slider {
            position: absolute;
            top: -2px;
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 5px;
            z-index: 1;
            background: var(--bg-secondary);
            outline: none;
            opacity: 1;
            -webkit-transition: .2s;
            transition: opacity .2s;
        }

        .slider:hover {
            opacity: 1;
        }

        .slider::-webkit-slider-thumb {
            position: relative;
            -webkit-appearance: none;
            appearance: none;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--accent-color);
            cursor: pointer;
            z-index: 3;
        }

        .play-controls {
            height: 70px;
            width: 320px;
            margin-top: -20%;
            background: var(--bg-card);
            border-radius: 10px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 2;
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .vynl {
            margin-top: -14%;
            margin-left: -4%;
            filter: drop-shadow(0 4px 6px var(--shadow-color));
        }

        .vynl-animation {
            animation: rotation 3s infinite linear;
        }

        @keyframes rotation {
            from {
                -webkit-transform: rotate(0deg);
            }
            to {
                -webkit-transform: rotate(359deg);
            }
        }

        .play-buttons {
            width: 100px;
            height: 30px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            color: var(--text-primary);
        }

        .play-buttons i {
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .play-buttons i:hover {
            color: var(--accent-color);
        }

        .volume-button {
            display: flex;
            justify-content: space-around;
            align-items: center;
            width: 30px;
            height: 30px;
            color: var(--text-primary);
            cursor: pointer;
        }

        .volume-button:hover {
            color: var(--accent-color);
        }

        .play-circle {
            width: 32px;
            height: 32px;
            background-image: url(https://cdn1.iconfinder.com/data/icons/ios-11-glyphs/30/circled_play-512.png);
            background-size: cover;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .play-circle:hover {
            transform: scale(1.1);
        }

        .pause-circle {
            width: 28px;
            height: 28px;
            background-image: url(https://cdn2.iconfinder.com/data/icons/ios-tab-bar/25/Pause_Filled-512.png);
            background-size: cover;
            background-repeat: no-repeat;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .pause-circle:hover {
            transform: scale(1.1);
        }

        /* Dark mode adjustments for turntable player */
        [data-theme="dark"] .play-circle {
            filter: invert(1);
        }

        [data-theme="dark"] .pause-circle {
            filter: invert(1);
        }

        @media (max-width: 768px) {
            .turntable-player {
                transform: scale(0.8);
            }
        }

        /* Song Info Section Styles */
        .song-info-container {
            max-width: 800px;
            margin: 0 auto 20px;
            background-color: var(--bg-card);
            border-radius: 10px;
            box-shadow: 0 10px 25px -5px var(--shadow-color), 0 8px 10px -6px var(--shadow-color);
            padding: 20px;
            transform: translateY(20px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
            position: relative;
            z-index: 5;
            margin-top: 20px; /* Adjusted from -50px to 20px to move it down */
            border: 1px solid var(--border-color);
        }

        .song-info-container.active {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .song-info-title {
            color: var(--accent-color);
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 8px;
            display: inline-block;
        }

        .song-info-details {
            color: var(--text-primary);
            line-height: 1.6;
        }

        .song-info-details h4 {
            color: var(--accent-color);
            margin: 15px 0 5px;
            font-weight: 600;
        }

        .song-info-details p {
            margin-bottom: 10px;
        }

        .song-info-details .highlight {
            background: linear-gradient(to right, var(--accent-color), var(--accent-color-dark));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .song-info-container {
                padding: 15px;
                margin: 20px auto;
                width: 90%;
            }

            .song-info-title {
                font-size: 1.2rem;
            }

            .song-info-details h4 {
                margin: 12px 0 4px;
            }

            .song-info-details p {
                font-size: 0.95rem;
                line-height: 1.5;
            }
        }

        @media (max-width: 480px) {
            .song-info-container {
                padding: 12px;
                margin: 15px auto;
                width: 95%;
            }

            .song-info-title {
                font-size: 1.1rem;
                padding-bottom: 6px;
                margin-bottom: 10px;
            }

            .song-info-details p {
                font-size: 0.9rem;
                margin-bottom: 8px;
            }
        }

        /* Start Music Process Button Styles */
        .start-music-process-btn {
            display: inline-block;
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.7);
            animation: pulse 2s infinite;
            transform: translateY(0);
            text-align: center;
            z-index: 5;
        }

        .start-music-process-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 15px 2px rgba(255, 165, 0, 0.9);
        }

        .start-music-process-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
        }

        .start-music-process-btn:hover::before {
            left: 100%;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(255, 165, 0, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 165, 0, 0);
            }
        }

        /* Hamburger Menu Styles */
        .hamburger-menu-button {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
        }

        .hamburger-icon {
            width: 24px;
            height: 2px;
            background-color: currentColor;
            position: relative;
            transition: background-color 0.3s ease;
        }

        .hamburger-icon:before,
        .hamburger-icon:after {
            content: '';
            position: absolute;
            width: 24px;
            height: 2px;
            background-color: currentColor;
            transition: transform 0.3s ease;
        }

        .hamburger-icon:before {
            top: -8px;
        }

        .hamburger-icon:after {
            bottom: -8px;
        }

        .mobile-menu-container {
            transition: all 0.3s ease;
        }

        /* Mobile menu spacing */
        .mobile-menu-container {
            padding-top: 12px;
        }

        .mobile-menu-container a {
            padding: 12px 0;
            border-bottom: 1px solid rgba(var(--border-color-rgb), 0.1);
            display: block;
        }

        .mobile-menu-container a:last-of-type {
            border-bottom: none;
        }

        /* Sticky Navigation Styles */
        .sticky-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background-color: var(--header-bg);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 40;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .sticky-nav.visible {
            transform: translateY(0);
        }

        .mobile-get-song-btn {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            /* Inherit all the animated button styles from primary-button */
            animation: pulse 2s infinite;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.7);
        }

        .mobile-get-song-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 0 15px 2px rgba(255, 165, 0, 0.9);
        }

        .mobile-get-song-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
        }

        .mobile-get-song-btn:hover::before {
            left: 100%;
        }

        /* Logo Styles */
        .logo-container {
            display: flex !important;
            align-items: center !important;
        }

        .site-logo {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            object-fit: contain;
            display: inline-block !important;
        }

        .site-logo-sm {
            width: 30px;
            height: 30px;
            margin-right: 8px;
            object-fit: contain;
            display: inline-block !important;
        }

        .animated-gradient-text {
            background: linear-gradient(90deg, #22c55e, #f97316, #8b5cf6);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: gradient 8s ease infinite;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* Theme Toggle Styles */
        .theme-toggle {
            cursor: pointer;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: var(--text-secondary);
        }

        .theme-toggle:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg); transition: background-color 0.3s ease;">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="hidden md:flex space-x-6 items-center">
                <a href="index.html" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Home</a>
                <a href="index.html#how-it-works" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">How It Works</a>
                <a href="index.html#examples" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Our Music</a>
                <a href="music-library.html" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Music Library</a>
                <a href="index.html#use-cases" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Use Cases</a>
                <a href="index.html#pricing" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Pricing</a>
                <a href="index.html#faq" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">FAQ</a>
                <a href="index.html#order-form-section" class="primary-button flex items-center">
                    <i class="fas fa-headphones-alt mr-2"></i>Get Your Custom Song
                </a>
                <div class="theme-toggle ml-4" id="themeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
            <div class="md:hidden flex items-center">
                <div class="theme-toggle mr-4" id="mobileThemeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
                <button id="mobile-menu-button" style="color: var(--text-primary);" class="focus:outline-none hamburger-menu-button">
                    <span class="hamburger-icon"></span>
                </button>
            </div>
        </nav>
        <div id="mobile-menu" class="hidden md:hidden px-6 pb-6 space-y-4 border-t theme-bg mobile-menu-container" style="border-color: var(--border-color); transition: background-color 0.3s ease;">
            <a href="index.html" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Home</a>
            <a href="index.html#how-it-works" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">How It Works</a>
            <a href="index.html#examples" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Our Music</a>
            <a href="music-library.html" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Music Library</a>
            <a href="index.html#use-cases" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Use Cases</a>
            <a href="index.html#pricing" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Pricing</a>
            <a href="index.html#faq" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">FAQ</a>
            <a href="index.html#order-form-section" class="primary-button w-full text-center flex items-center justify-center mt-4">
                <i class="fas fa-headphones-alt mr-2"></i>Get Your Custom Song
            </a>
        </div>
    </header>

    <div id="sticky-nav" class="sticky-nav">
        <div class="container mx-auto px-6 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo-sm">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-4 items-center">
                <a href="index.html" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Home</a>
                <a href="index.html#how-it-works" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">How It Works</a>
                <a href="index.html#examples" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Our Music</a>
                <a href="music-library.html" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Music Library</a>
                <a href="index.html#use-cases" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Use Cases</a>
                <a href="index.html#pricing" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Pricing</a>
                <a href="index.html#order-form-section" class="primary-button text-sm py-2 px-4">Get Your Custom Song</a>
                <div class="theme-toggle ml-4" id="stickyThemeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
            <!-- Mobile Navigation -->
            <div class="md:hidden flex items-center justify-center ml-2">
                <a href="index.html#order-form-section" class="primary-button text-xs py-1 px-2 mobile-get-song-btn">Get Song</a>
            </div>
        </div>
    </div>

    <!-- Music Library Content -->
    <div class="player-extended">
        <div class="bgBox">
            <img src="https://images.unsplash.com/photo-1511275539165-cc46b1ee89bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" class="polyrize" />
        </div>
        <h1><span>Aifrobeats Music Library</span></h1>
    </div>

    <section class="main">
        <div id="player">
            <section class="player">
                <div class="menu-button"><span></span></div>
                <section class="playlist" style="display: block;">
                    <ul id="trackList">
                        <!-- Tracks will be populated by JavaScript -->
                    </ul>
                    <p class="info mt-6 text-center text-sm" style="color: var(--text-muted);">
                        Browse our collection of AI-generated, human-curated Afrobeats tracks.
                        Click any track to play it in our custom music player.
                    </p>
                </section>

                <figure class="currentTrack">
                    <div class="currentTrackCover"></div>
                    <img id="currentTrackImage" src="https://images.unsplash.com/photo-1511275539165-cc46b1ee89bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" />
                    <figcaption id="currentTrackCaption">Aifrobeats<br>Custom Music Library</figcaption>
                </figure>
                <section class="controls">
                    <h2 class="title" id="currentTrackTitle">Select a track to play</h2>
                    <div class="audio">
                        <div class="audio-controls">
                            <div class="play-pause" id="libraryPlayPauseButton">
                                <p class="play" style="display: block;"><i class="fa fa-play"></i></p>
                                <p class="pause" style="display: none;"><i class="fa fa-pause"></i></p>
                            </div>
                        </div>
                        <div class="scrubber">
                            <div class="progress"></div>
                            <div class="loaded"></div>
                        </div>
                        <div class="time">
                            <em class="played">00:00</em>
                            <strong class="duration">00:00</strong>
                        </div>
                    </div>
                    <div class="buttons">
                        <div class="repeat" id="repeatButton"><i class="fa fa-refresh"></i></div>
                        <div class="shuffle" id="shuffleButton"><i class="fa fa-random"></i></div>
                        <div class="back-to-list" id="backToListButton"><i class="fa fa-list"></i></div>
                    </div>
                </section>
            </section>
        </div>
    </section>

    <!-- Music Player Toggle Button -->
    <div class="music-player-toggle" id="musicPlayerToggle">
        <i class="fas fa-music"></i>
    </div>

    <!-- Fixed Music Player -->
    <div class="music-player-container active" id="musicPlayerContainer">
        <div class="music-player">
            <!-- Album Art with Vinyl Turntable -->
            <div class="music-player-album vinyl-container">
                <img id="playerAlbumArt" src="https://images.unsplash.com/photo-1511275539165-cc46b1ee89bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80" alt="Album Art">
                <img src="https://pngimg.com/uploads/vinyl/vinyl_PNG21.png" id="vynl-id" class="vinyl-record" alt="Vinyl Record">
            </div>

            <!-- Track Info -->
            <div class="music-player-info">
                <p class="music-player-title" id="playerTitle">Select a track to play</p>
                <p class="music-player-artist" id="playerArtist">Aifrobeats Music Library</p>
            </div>

            <!-- Controls -->
            <div class="music-player-controls">
                <button class="music-player-control" id="prevButton">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button class="music-player-control play-pause" id="playPauseButton">
                    <i class="fas fa-play" id="playPauseIcon"></i>
                </button>
                <button class="music-player-control" id="nextButton">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <!-- Progress Bar -->
            <div class="music-player-progress-container" id="progressContainer">
                <div class="music-player-progress" id="progressBar">
                    <div class="music-player-progress-handle"></div>
                </div>
            </div>

            <!-- Time -->
            <div class="music-player-time">
                <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
            </div>

            <!-- Volume -->
            <div class="music-player-volume-container">
                <div class="music-player-volume-icon" id="volumeIcon">
                    <i class="fas fa-volume-up"></i>
                </div>
                <div class="music-player-volume-slider" id="volumeSlider">
                    <div class="music-player-volume-level" id="volumeLevel"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Song Info Section -->
    <div class="song-info-container" id="songInfoContainer">
        <div class="song-info-content">
            <h3 class="song-info-title" id="songInfoTitle">Song Story</h3>
            <div class="song-info-details" id="songInfoDetails">
                <p>Select a song to view its story and background.</p>
            </div>
            <a href="index.html#order-form-section" class="start-music-process-btn">
                <i class="fas fa-music mr-2"></i>Start Your Music Journey
            </a>
        </div>
    </div>

    <!-- Hidden elements for compatibility with turntable player code -->
    <div style="display: none;">
        <div id="now-playing-board-id"></div>
        <div id="now-playing-board-bottom-bar-id"></div>
        <div id="playpause" class="play-circle"></div>
        <div id="completionBar"></div>
        <input type="range" id="range" min="1" max="100" value="1">
    </div>

    <!-- Hidden Audio Element -->
    <audio id="audioPlayer" preload="auto"></audio>

    <!-- Pidgin Chat CTA Section -->
    <section class="py-16 md:py-24 relative overflow-hidden">
        <div class="fixed-bg absolute inset-0 z-0"></div>
        <div class="overlay absolute inset-0 bg-black bg-opacity-60 z-0"></div>
        <div class="container mx-auto px-6 text-center relative z-10">
            <h2 class="text-3xl md:text-4xl font-bold mb-6 text-white">Create Your Afrobeat Lyrics with Pidgin Chat</h2>
            <div class="max-w-xs mx-auto">
                <a href="https://aifrobeats.com/pidginchat" class="primary-button flex items-center justify-center">
                    <i class="fas fa-paper-plane mr-2"></i> Start Chatting
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Aifrobeats Info -->
                <div>
                    <div class="flex items-center text-green-500 mb-4">
                        <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-2">
                        <h3 class="text-xl font-bold animated-gradient-text">Aifrobeats</h3>
                    </div>
                    <p class="text-gray-400 mb-4">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-2">
                        <i class="fas fa-envelope text-green-500 mr-2"></i>
                        <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-2"></i>
                        <a href="tel:+2347038808350" class="text-gray-400 hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="index.html" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="index.html#how-it-works" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>How It Works
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                        <li>
                            <a href="index.html#faq" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>FAQ
                            </a>
                        </li>
                        <li>
                            <a href="index.html#order-form-section" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Order Now
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Connect With Us</h3>
                    <p class="text-gray-400 mb-4">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow p-2">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-8 pt-6 border-t border-gray-800 text-center text-sm">
                <p class="text-gray-400">&copy; 2025 AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="text-gray-400 hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline text-gray-600">|</span>
                    <a href="privacy.html" class="text-gray-400 hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            once: true
        });

        // Theme Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const mobileThemeToggle = document.getElementById('mobileThemeToggle');
            const stickyThemeToggle = document.getElementById('stickyThemeToggle');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const stickyNav = document.getElementById('sticky-nav');

            // Check for saved theme preference or use device preference
            const savedTheme = localStorage.getItem('theme') ||
                (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

            // Apply the saved theme
            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }

            // Theme toggle event
            themeToggle.addEventListener('click', function() {
                toggleTheme();
            });

            // Mobile theme toggle event
            if (mobileThemeToggle) {
                mobileThemeToggle.addEventListener('click', function() {
                    toggleTheme();
                });
            }

            // Sticky theme toggle event
            if (stickyThemeToggle) {
                stickyThemeToggle.addEventListener('click', function() {
                    toggleTheme();
                });
            }

            function toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                if (newTheme === 'dark') {
                    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                } else {
                    themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                }
            }

            // Mobile menu toggle
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Sticky navigation
            if (stickyNav) {
                let lastScrollTop = 0;
                const headerHeight = document.querySelector('header').offsetHeight;

                window.addEventListener('scroll', function() {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    // Show sticky nav when scrolling down past the header
                    if (scrollTop > headerHeight && scrollTop > lastScrollTop) {
                        stickyNav.classList.add('visible');
                    }
                    // Hide sticky nav when scrolling up to the top
                    else if (scrollTop < lastScrollTop || scrollTop <= headerHeight) {
                        stickyNav.classList.remove('visible');
                    }

                    lastScrollTop = scrollTop;
                });
            }
        });

        // Music Library JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const musicPlayerToggle = document.getElementById('musicPlayerToggle');
            const musicPlayerContainer = document.getElementById('musicPlayerContainer');
            const audioPlayer = document.getElementById('audioPlayer');
            const playPauseButton = document.getElementById('playPauseButton');
            const playPauseIcon = document.getElementById('playPauseIcon');
            const prevButton = document.getElementById('prevButton');
            const nextButton = document.getElementById('nextButton');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const currentTimeElement = document.getElementById('currentTime');
            const totalTimeElement = document.getElementById('totalTime');
            const volumeSlider = document.getElementById('volumeSlider');
            const volumeLevel = document.getElementById('volumeLevel');
            const volumeIcon = document.getElementById('volumeIcon');
            const playerAlbumArt = document.getElementById('playerAlbumArt');
            const playerTitle = document.getElementById('playerTitle');
            const playerArtist = document.getElementById('playerArtist');

            // Library player elements
            const trackList = document.getElementById('trackList');
            const currentTrackImage = document.getElementById('currentTrackImage');
            const currentTrackTitle = document.getElementById('currentTrackTitle');
            const currentTrackCaption = document.getElementById('currentTrackCaption');
            const libraryPlayPauseButton = document.getElementById('libraryPlayPauseButton');
            const libraryPlayButton = document.querySelector('.play');
            const libraryPauseButton = document.querySelector('.pause');
            const libraryScrubber = document.querySelector('.scrubber');
            const libraryLoaded = document.querySelector('.loaded');
            const libraryPlayed = document.querySelector('.played');
            const libraryDuration = document.querySelector('.duration');
            const menuButton = document.querySelector('.menu-button');
            const playlist = document.querySelector('.playlist');
            const bgBox = document.querySelector('.bgBox');
            const bgImage = document.querySelector('.bgBox img');

            // Song descriptions
            const songDescriptions = {
                "Delia": `<h4>Song Story</h4>
                <p>This catchy jingle was created for Delia, a popular detergent brand looking to enhance their market presence through music.</p>
                <h4>Client Request</h4>
                <p>The business wanted an upbeat, danceable rhythm that would be memorable and associate positive feelings with their brand.</p>
                <h4>Production Notes</h4>
                <p>Our team crafted this track with infectious Afrobeats rhythms and a simple, memorable hook that customers can dance to. The energetic production style reflects the brand's vibrant and effective cleaning power.</p>`,

                "Yemi": `<h4>Song Story</h4>
                <p>This beautiful wedding anthem was created for a man expressing his undying love for his beloved Yemi. The song captures the essence of their relationship and celebrates their union with heartfelt lyrics and a melodic Afrobeats rhythm.</p>
                <h4>Client Request</h4>
                <p>The client wanted a personalized wedding song that would express his deep feelings for Yemi, incorporating traditional African musical elements with modern production.</p>
                <h4>Production Notes</h4>
                <p>Our team crafted this piece with authentic African percussion, soulful vocals, and romantic lyrics that tell the story of their love journey. The song has become a cherished memento of their special day.</p>`,

                "Do-It": `<h4>Song Story</h4>
                <p>This high-energy gym anthem was commissioned by FitLife Gym to motivate their members during workout sessions. The driving beat and motivational lyrics make it perfect for maintaining energy during intense training.</p>
                <h4>Client Request</h4>
                <p>FitLife Gym wanted a custom track that would energize their clients and become a signature sound for their brand, emphasizing determination and the power of pushing through challenges.</p>
                <h4>Production Notes</h4>
                <p>Produced by DJ Baba, this track features a powerful 128 BPM tempo ideal for workout pacing, with motivational vocal hooks and a dynamic arrangement that builds in intensity to match workout progression.</p>`,

                "ChicaBenita": `<h4>Song Story</h4>
                <p>This heartwarming family song tells the story of a man coming home to see his children playing with their mother. The gentle melody and touching lyrics celebrate the beauty of family bonds and everyday moments of joy.</p>
                <h4>Client Request</h4>
                <p>The client wanted a song that would capture the special feeling of family togetherness, with lyrics that specifically mention the joy of seeing his children and wife enjoying time together.</p>
                <h4>Production Notes</h4>
                <p>We created a warm, melodic arrangement with gentle percussion and acoustic elements to complement the tender lyrics. The song has a nostalgic quality that enhances its emotional impact.</p>`,

                "Sarah 1": `<h4>Song Story</h4>
                <p>This celebratory melody was created for a man honoring his loving wife Sarah. The first version features a more traditional Afrobeats arrangement with emphasis on percussion and rhythm.</p>
                <h4>Client Request</h4>
                <p>The client wanted to surprise his wife with a song that expressed his appreciation for her love and support throughout their relationship. He requested a song that would make her feel special and celebrated.</p>
                <h4>Production Notes</h4>
                <p>This version emphasizes traditional African rhythms with modern production techniques, creating a joyful celebration of love that balances cultural authenticity with contemporary sound.</p>`,

                "Sarah 2": `<h4>Song Story</h4>
                <p>This alternative arrangement of "Sarah's Song" offers a different musical interpretation of the same heartfelt tribute. This version features more prominent melodic elements and a softer approach to the rhythm section.</p>
                <h4>Client Request</h4>
                <p>After hearing the first version, the client requested an alternative arrangement that would offer a different emotional quality while maintaining the core message of love and appreciation.</p>
                <h4>Production Notes</h4>
                <p>Our team rearranged the original composition with more emphasis on melodic instruments and vocal harmonies, creating a more intimate and reflective mood compared to the first version.</p>`,

                "Baby Cheat": `<h4>Song Story</h4>
                <p>This catchy track was commissioned by a content creator specifically for Instagram and TikTok videos. With its memorable hook and danceable beat, it's designed to capture attention in short-form content.</p>
                <h4>Client Request</h4>
                <p>The content creator needed a distinctive track that would help their videos stand out and potentially go viral. They wanted something with a contemporary Afrobeats sound that would appeal to a young audience.</p>
                <h4>Production Notes</h4>
                <p>We crafted this track with social media in mind, ensuring it has an immediate impact and features sections that work well for dance challenges and trending content formats.</p>`,

                "Back it Up": `<h4>Song Story</h4>
                <p>This energetic dance floor anthem was created for a downtown club in Accra for their popular disco nights. The pulsating rhythm and call-to-action lyrics make it perfect for getting crowds moving.</p>
                <h4>Client Request</h4>
                <p>The club owners wanted a signature track that would become associated with their venue and create memorable moments during peak hours. They specified a need for something that would appeal to both locals and tourists.</p>
                <h4>Production Notes</h4>
                <p>We blended classic disco elements with contemporary Afrobeats to create a cross-generational appeal. The track features call-and-response sections designed to engage the crowd.</p>`,

                "Comot for Road": `<h4>Song Story</h4>
                <p>This vibrant street-inspired track was commissioned by a content creator for their TikTok and Instagram channels. The authentic street vibe and catchy phrases make it perfect for trending challenges.</p>
                <h4>Client Request</h4>
                <p>The creator wanted a track that captured the energy and language of urban African youth culture, with phrases and slang that would resonate with their audience and inspire creative video content.</p>
                <h4>Production Notes</h4>
                <p>We incorporated authentic street sounds and vernacular expressions into a contemporary production, creating a track that feels both genuine and highly shareable.</p>`,

                "Cowgirl": `<h4>Song Story</h4>
                <p>This unique fusion track was requested by a South Texas man as a special gift for his cowgirl girlfriend. It blends Western country elements with Afrobeats for a truly distinctive sound.</p>
                <h4>Client Request</h4>
                <p>The client wanted to surprise his girlfriend with a song that celebrated her cowgirl spirit while incorporating his appreciation for African music. He requested something that would make her smile and feel appreciated.</p>
                <h4>Production Notes</h4>
                <p>Our team experimented with combining traditional country instruments like slide guitar with Afrobeats percussion, creating a cross-cultural musical experience that honors both traditions.</p>`,

                "Dancefloor Confessions": `<h4>Song Story</h4>
                <p>This sophisticated late-night track is part of an exclusive set created for a high-end Lagos club catering to an upscale clientele. The smooth production and mature themes make it perfect for the late hours.</p>
                <h4>Client Request</h4>
                <p>The club owner commissioned a series of tracks that would appeal specifically to their wealthy patrons, with lyrics and production values that reflect luxury lifestyle and exclusive experiences.</p>
                <h4>Production Notes</h4>
                <p>We used premium sound design and sophisticated arrangements to create an atmosphere of exclusivity. The track features subtle references to high-end brands and experiences familiar to the target audience.</p>`,

                "Fake Friends": `<h4>Song Story</h4>
                <p>This thought-provoking track was created for a YouTube content creator who wanted music that would complement videos discussing relationships and personal growth. The honest lyrics address the universal experience of betrayal.</p>
                <h4>Client Request</h4>
                <p>The creator needed background music that would enhance their commentary on friendship, loyalty, and recognizing inauthentic relationships. They wanted something that would resonate emotionally with viewers.</p>
                <h4>Production Notes</h4>
                <p>We balanced reflective musical elements with a compelling rhythm to support the narrative without overwhelming it. The production allows space for voice-overs while maintaining emotional impact.</p>`,

                "Freedom": `<h4>Song Story</h4>
                <p>This inspirational anthem is part of the exclusive collection created for a prestigious Lagos club. The uplifting message and powerful production make it a highlight of their musical programming.</p>
                <h4>Client Request</h4>
                <p>The club owner wanted a track that would create a sense of liberation and celebration for their patrons, with themes of success and overcoming obstacles that would resonate with their ambitious clientele.</p>
                <h4>Production Notes</h4>
                <p>We incorporated traditional African choir elements with modern production techniques to create a sense of triumph and celebration. The arrangement builds to create powerful emotional peaks.</p>`,

                "Hood Love": `<h4>Song Story</h4>
                <p>This authentic street romance track is another gem from the exclusive Lagos club collection. It tells a story of love that flourishes despite challenging circumstances, resonating with listeners from all backgrounds.</p>
                <h4>Client Request</h4>
                <p>The club owner wanted a track that would celebrate authentic relationships with a narrative that acknowledges struggle while emphasizing the power of connection and loyalty.</p>
                <h4>Production Notes</h4>
                <p>We balanced gritty urban production elements with softer melodic components to create contrast that enhances the storytelling. The track features subtle street sounds that add authenticity.</p>`,

                "Jungle": `<h4>Song Story</h4>
                <p>This wild, rhythm-driven track continues the exclusive Lagos club series with a metaphorical exploration of urban life as a jungle. The intense percussion and primal energy make it a standout in the collection.</p>
                <h4>Client Request</h4>
                <p>The club owner requested a track that would capture the competitive, sometimes chaotic nature of city life and business, with a powerful sound that would energize their ambitious patrons.</p>
                <h4>Production Notes</h4>
                <p>We incorporated actual jungle sounds and traditional African percussion techniques to create an immersive sonic experience. The production features multiple rhythm layers that create a complex, engaging texture.</p>`,

                "Last Man Standing": `<h4>Song Story</h4>
                <p>This triumphant anthem completes the exclusive Lagos club collection with a powerful message of perseverance and ultimate victory. It celebrates those who endure challenges and emerge successful.</p>
                <h4>Client Request</h4>
                <p>The club owner wanted a climactic track that would serve as the emotional peak of their musical programming, inspiring their patrons and reinforcing themes of resilience and achievement.</p>
                <h4>Production Notes</h4>
                <p>We created a production that builds dramatically throughout, incorporating military-inspired percussion and victorious brass elements. The arrangement is designed to create a sense of overcoming and triumph.</p>`,

                "Life": `<h4>Song Story</h4>
                <p>This reflective, spiritually-themed track was created for a Christian content creator's TikTok channel. The thoughtful lyrics and uplifting melody make it perfect for content focused on faith and personal growth.</p>
                <h4>Client Request</h4>
                <p>The creator wanted music that would complement their inspirational content while incorporating subtle Christian themes and values. They needed something that would appeal to a broad audience while remaining true to their faith.</p>
                <h4>Production Notes</h4>
                <p>We balanced contemporary production with gospel-influenced harmonies and chord progressions. The arrangement leaves space for contemplation while maintaining an engaging rhythm.</p>`,

                "Life (Funk)": `<h4>Song Story</h4>
                <p>This funkier variation of "Life" offers the same inspirational message with a more groove-oriented production. Created as an alternative for the Christian content creator, it brings a different energy to the same core themes.</p>
                <h4>Client Request</h4>
                <p>After hearing the original version, the creator requested a more upbeat alternative that would work for their more celebratory and energetic content while maintaining the spiritual essence.</p>
                <h4>Production Notes</h4>
                <p>We reimagined the original track with funk-inspired bass lines, more prominent rhythm guitar, and a generally more dynamic arrangement while preserving the meaningful lyrics.</p>`,

                "My Money": `<h4>Song Story</h4>
                <p>This confident financial anthem was created for a Facebook content creator who focuses on entrepreneurship and wealth-building. The bold lyrics and assertive production reflect themes of financial success and independence.</p>
                <h4>Client Request</h4>
                <p>The creator wanted background music that would enhance their content about financial literacy, entrepreneurship, and wealth creation. They needed something that projected confidence and success.</p>
                <h4>Production Notes</h4>
                <p>We incorporated sound elements associated with wealth (cash registers, coins) into a contemporary production with strong bass and percussion. The track is designed to inspire confidence and ambition.</p>`,

                "Steady for You": `<h4>Song Story</h4>
                <p>This romantic track from the Lagos club collection celebrates committed relationships and loyalty. The smooth production and sincere lyrics make it a favorite for couples at the venue.</p>
                <h4>Client Request</h4>
                <p>The club owner wanted a track that would appeal specifically to couples among their clientele, with themes of commitment and enduring love that would create a romantic atmosphere.</p>
                <h4>Production Notes</h4>
                <p>We created a smooth, mid-tempo arrangement with subtle R&B influences that complement the Afrobeats foundation. The production features intimate vocal performances and warm instrumental textures.</p>`,

                "Vibes": `<h4>Song Story</h4>
                <p>This feel-good track from the Lagos club collection is designed purely for positive energy and good times. With its uplifting production and carefree lyrics, it's perfect for setting a joyful atmosphere.</p>
                <h4>Client Request</h4>
                <p>The club owner wanted a track that would instantly lift the mood and create a sense of carefree enjoyment, with a universal appeal that would work across different times of the night.</p>
                <h4>Production Notes</h4>
                <p>We focused on creating an irresistibly positive sound with bright instrumentation and uplifting chord progressions. The arrangement is designed to create an immediate mood enhancement.</p>`,

                "Willie Willie": `<h4>Song Story</h4>
                <p>This playful, dance-oriented track was created for a TikTok content creator who specializes in dance challenges. The repetitive, catchy hook and dynamic rhythm make it perfect for choreography.</p>
                <h4>Client Request</h4>
                <p>The creator wanted a track specifically designed to launch a new dance challenge, with a distinctive hook that would be easy to remember and a rhythm that would inspire creative choreography.</p>
                <h4>Production Notes</h4>
                <p>We structured the track with clear sections that work well for dance routines, with tempo and energy shifts that allow for choreographic variety. The production emphasizes the beat and features call-outs that can guide dance moves.</p>`,

                "Milele Na Wewe": `<h4>Song Story</h4>
                <p>This romantic Valentine's Day song was created for a man who wanted to express his deep love and commitment to his partner Stella. The title "Milele Na Wewe" means "Forever With You" in Swahili, symbolizing eternal love.</p>
                <h4>Client Request</h4>
                <p>The client wanted a heartfelt love song that would make his partner feel special on Valentine's Day. He requested a blend of modern Afrobeats with romantic lyrics that specifically mentioned his partner's name and their journey together.</p>
                <h4>Production Notes</h4>
                <p>We crafted this track with gentle percussion, warm melodic elements, and sincere vocals that convey genuine emotion. The arrangement builds gradually to create an intimate atmosphere perfect for a romantic occasion.</p>`,

                "Eagles for Glory": `<h4>Song Story</h4>
                <p>This energetic anthem was commissioned by the principal of Glorious Heights School in Lagos as part of an initiative to introduce students to the rich heritage of Afrobeat music. The song captures the school's spirit while paying homage to Fela Kuti's influential style.</p>
                <h4>Client Request</h4>
                <p>The principal needed a catchy, raw Afrobeat anthem that would resonate with students while introducing them to the nostalgic sounds of Fela-esque music. The track needed to embody the school's values of excellence and achievement.</p>
                <h4>Production Notes</h4>
                <p>We incorporated authentic Afrobeat elements including horn sections, complex polyrhythms, and call-and-response vocals that capture the essence of the genre's golden era. The production balances educational value with contemporary appeal to engage the younger generation.</p>`
            };

            // Track list with metadata
            const tracks = [
                {
                    title: "Delia's Song",
                    artist: "Detergent Brand Jingle",
                    src: "music/Delia.mp3",
                    albumArt: "images/delia.png",
                    infoKey: "Delia"
                },
                {
                    title: "Milele Na Wewe",
                    artist: "Valentine's Day Song for Stella",
                    src: "music/Milele Na Wewe.mp3",
                    albumArt: "https://images.unsplash.com/photo-1655683576616-c40706fa79f2?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Milele Na Wewe"
                },
                {
                    title: '"Chica Benita" Family Moment',
                    artist: "Heartfelt Song for Wife and Children",
                    src: "music/ChicaBenita.mp3",
                    albumArt: "https://images.unsplash.com/photo-1609220136736-443140cffec6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "ChicaBenita"
                },
                {
                    title: "Sarah's Song (Version 1)",
                    artist: "Celebration Melody",
                    src: "music/Sarah 1.mp3",
                    albumArt: "https://images.unsplash.com/photo-1614291129408-3dd5436942e6?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Sarah 1"
                },
                {
                    title: "Sarah's Song (Version 2)",
                    artist: "Alternative Arrangement",
                    src: "music/Sarah 2.mp3",
                    albumArt: "https://images.unsplash.com/photo-1744063687632-856c64c3f1de?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Sarah 2"
                },
                {
                    title: "Baby Cheat",
                    artist: "Aifrobeats Original",
                    src: "music/Baby Cheat.mp3",
                    albumArt: "https://images.unsplash.com/photo-1501386761578-eac5c94b800a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Baby Cheat"
                },
                {
                    title: "Back it Up",
                    artist: "Dance Floor Anthem",
                    src: "music/Back it Up.mp3",
                    albumArt: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Back it Up"
                },
                {
                    title: "Comot for Road",
                    artist: "Street Vibes",
                    src: "music/Comot for Road 1.mp3",
                    albumArt: "https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Comot for Road"
                },
                {
                    title: "Cowgirl",
                    artist: "Party Starter",
                    src: "music/Cowgirl.mp3",
                    albumArt: "https://images.unsplash.com/photo-1523528283115-9bf9b1699245?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Cowgirl"
                },
                {
                    title: "Dancefloor Confessions",
                    artist: "Late Night Vibes",
                    src: "music/Dancefloor Confessions.mp3",
                    albumArt: "https://images.unsplash.com/photo-1429962714451-bb934ecdc4ec?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Dancefloor Confessions"
                },
                {
                    title: "Fake Friends",
                    artist: "Real Talk",
                    src: "music/Fake Friends.mp3",
                    albumArt: "https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Fake Friends"
                },
                {
                    title: "Freedom",
                    artist: "Inspirational Anthem",
                    src: "music/Freedom.mp3",
                    albumArt: "https://images.unsplash.com/photo-1506268919522-a927511962a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Freedom"
                },
                {
                    title: "Hood Love",
                    artist: "Street Romance",
                    src: "music/Hood Love.mp3",
                    albumArt: "https://images.unsplash.com/photo-1581952976147-5a2d15560349?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Hood Love"
                },
                {
                    title: "Jungle",
                    artist: "Wild Rhythms",
                    src: "music/Jungle.mp3",
                    albumArt: "https://images.unsplash.com/photo-1502082553048-f009c37129b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Jungle"
                },
                {
                    title: "Last Man Standing",
                    artist: "Victory Anthem",
                    src: "music/Last Man Standing.mp3",
                    albumArt: "https://images.unsplash.com/photo-1541532713592-79a0317b6b77?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Last Man Standing"
                },
                {
                    title: "Life (Funk)",
                    artist: "Groove Session",
                    src: "music/Life (Funk).mp3",
                    albumArt: "https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Life (Funk)"
                },
                {
                    title: "Life",
                    artist: "Reflective Journey",
                    src: "music/Life.mp3",
                    albumArt: "https://images.unsplash.com/photo-1519834785169-98be25ec3f84?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Life"
                },
                {
                    title: "My Money",
                    artist: "Financial Anthem",
                    src: "music/My Money.mp3",
                    albumArt: "https://images.unsplash.com/photo-1526304640581-d334cdbbf45e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "My Money"
                },
                {
                    title: "Steady for You",
                    artist: "Relationship Goals",
                    src: "music/Steady for You.mp3",
                    albumArt: "https://images.unsplash.com/photo-1518199266791-5375a83190b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Steady for You"
                },
                {
                    title: "Vibes",
                    artist: "Feel Good Energy",
                    src: "music/Vibes.mp3",
                    albumArt: "https://images.unsplash.com/photo-1470225620780-dba8ba36b745?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Vibes"
                },
                {
                    title: "Willie Willie",
                    artist: "Dance Celebration",
                    src: "music/Willie Willie.mp3",
                    albumArt: "https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Willie Willie"
                },
                {
                    title: "Eagles for Glory",
                    artist: "Glorious Heights School Anthem",
                    src: "music/Eagles for Glory.mp3",
                    albumArt: "https://images.unsplash.com/photo-1686721454934-d874ad6e2ce7?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80",
                    infoKey: "Eagles for Glory"
                }
            ];

            // Current track index
            let currentTrackIndex = 0;
            let isPlaylistVisible = true; // Start with playlist visible
            let isShuffleActive = false;
            let isLoopActive = false;
            let originalTracks = [...tracks]; // Keep a copy of the original track order

            // Initialize player
            function initPlayer() {
                // Set initial volume
                audioPlayer.volume = 0.7;
                updateVolumeUI();

                // Populate track list
                populateTrackList();

                // Set up the library player
                setupLibraryPlayer();

                // Make sure the player is visible
                if (musicPlayerContainer.classList.contains('active')) {
                    musicPlayerToggle.innerHTML = '<i class="fas fa-times"></i>';
                }

                // Load the first track
                loadTrack(currentTrackIndex);

                // Set up shuffle, loop, and back to list buttons
                setupControlButtons();

                console.log('Music player initialized');
            }

            // Set up control buttons (shuffle, loop, back to list)
            function setupControlButtons() {
                const shuffleButton = document.getElementById('shuffleButton');
                const repeatButton = document.getElementById('repeatButton');
                const backToListButton = document.getElementById('backToListButton');

                // Shuffle button
                shuffleButton.addEventListener('click', () => {
                    isShuffleActive = !isShuffleActive;

                    if (isShuffleActive) {
                        shuffleButton.classList.add('active');
                        shuffleTracks();
                    } else {
                        shuffleButton.classList.remove('active');
                        // Restore original order
                        tracks = [...originalTracks];
                        // Keep current track as current
                        const currentTrack = originalTracks[currentTrackIndex];
                        currentTrackIndex = tracks.findIndex(track => track.src === currentTrack.src);
                        // Repopulate track list
                        populateTrackList();
                        updateLibraryPlayer();
                    }
                });

                // Repeat button
                repeatButton.addEventListener('click', () => {
                    isLoopActive = !isLoopActive;

                    if (isLoopActive) {
                        repeatButton.classList.add('active');
                        audioPlayer.loop = true;
                    } else {
                        repeatButton.classList.remove('active');
                        audioPlayer.loop = false;
                    }
                });

                // Back to list button
                backToListButton.addEventListener('click', () => {
                    // Show the playlist
                    isPlaylistVisible = true;
                    playlist.style.display = 'block';
                    menuButton.classList.add('active');

                    // Scroll to the current track
                    const activeTrack = document.querySelector(`.track[data-index="${currentTrackIndex}"]`);
                    if (activeTrack) {
                        activeTrack.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });
            }

            // Shuffle tracks
            function shuffleTracks() {
                // Save current track
                const currentTrack = tracks[currentTrackIndex];

                // Fisher-Yates shuffle algorithm
                for (let i = tracks.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [tracks[i], tracks[j]] = [tracks[j], tracks[i]];
                }

                // Find the new index of the current track
                currentTrackIndex = tracks.findIndex(track => track.src === currentTrack.src);

                // Repopulate track list
                populateTrackList();
                updateLibraryPlayer();
            }

            // Populate the track list
            function populateTrackList() {
                trackList.innerHTML = '';

                tracks.forEach((track, index) => {
                    const li = document.createElement('li');
                    li.className = 'track';
                    li.setAttribute('data-index', index);

                    li.innerHTML = `
                        <div>
                            <figure>
                                <img src="${track.albumArt}" />
                            </figure>
                            <label>
                                <strong>${track.title}</strong>
                                <span>${track.artist}</span>
                                <time>00:00</time>
                            </label>
                        </div>
                    `;

                    li.addEventListener('click', () => {
                        currentTrackIndex = index;
                        loadTrack(currentTrackIndex);
                        playTrack();
                        updateLibraryPlayer();
                    });

                    trackList.appendChild(li);
                });
            }

            // Set up the library player
            function setupLibraryPlayer() {
                // Set initial state
                libraryPauseButton.style.display = 'none';
                libraryPlayButton.style.display = 'block';

                // Add event listeners for the main play/pause button
                libraryPlayPauseButton.addEventListener('click', () => {
                    if (audioPlayer.paused) {
                        playTrack();
                    } else {
                        pauseTrack();
                    }
                });

                // Individual play/pause buttons
                libraryPlayButton.addEventListener('click', () => {
                    playTrack();
                });

                libraryPauseButton.addEventListener('click', () => {
                    pauseTrack();
                });

                libraryScrubber.addEventListener('click', (e) => {
                    const scrubberWidth = libraryScrubber.offsetWidth;
                    const clickPosition = e.offsetX;
                    const seekPercentage = clickPosition / scrubberWidth;

                    if (audioPlayer.duration) {
                        audioPlayer.currentTime = audioPlayer.duration * seekPercentage;
                        updateProgress();
                    }
                });

                menuButton.addEventListener('click', () => {
                    togglePlaylist();
                });
            }

            // Toggle playlist visibility
            function togglePlaylist() {
                isPlaylistVisible = !isPlaylistVisible;

                if (isPlaylistVisible) {
                    playlist.style.display = 'block';
                    menuButton.classList.add('active');

                    // Scroll to the current track
                    const activeTrack = document.querySelector(`.track[data-index="${currentTrackIndex}"]`);
                    if (activeTrack) {
                        setTimeout(() => {
                            activeTrack.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                    }
                } else {
                    playlist.style.display = 'none';
                    menuButton.classList.remove('active');
                }
            }

            // Update the library player
            function updateLibraryPlayer() {
                const track = tracks[currentTrackIndex];

                currentTrackImage.src = track.albumArt;
                currentTrackTitle.textContent = track.title;
                currentTrackCaption.innerHTML = `${track.artist}<br>${track.title}`;

                // Update background
                bgImage.src = track.albumArt;
                createBgImage(currentTrackImage);

                // Update the active track in the list
                const trackItems = document.querySelectorAll('.track');
                trackItems.forEach(item => {
                    item.classList.remove('active');
                });

                const activeTrack = document.querySelector(`.track[data-index="${currentTrackIndex}"]`);
                if (activeTrack) {
                    activeTrack.classList.add('active');
                }
            }

            // Create background image
            function createBgImage(src) {
                bgBox.style.backgroundImage = `url(${src.src})`;
            }

            // Song info container
            const songInfoContainer = document.getElementById('songInfoContainer');
            const songInfoDetails = document.getElementById('songInfoDetails');
            const songInfoTitle = document.getElementById('songInfoTitle');

            // Load track
            function loadTrack(index) {
                const track = tracks[index];
                console.log('Loading track:', index, track.title);

                // Set new track info
                audioPlayer.src = track.src;
                playerAlbumArt.src = track.albumArt;
                playerTitle.textContent = track.title;
                playerArtist.textContent = track.artist;

                // Reset progress
                progressBar.style.width = '0%';
                libraryLoaded.style.width = '0%';
                currentTimeElement.textContent = '0:00';
                totalTimeElement.textContent = '0:00';
                libraryPlayed.textContent = '00:00';
                libraryDuration.textContent = '00:00';

                // Reset turntable player
                var completionBar = document.getElementById("completionBar");
                var range = document.getElementById("range");
                if (completionBar && range) {
                    completionBar.style.width = '0%';
                    range.value = 1;
                }

                // Reset vinyl animation if audio is paused
                if (audioPlayer.paused) {
                    const vinylRecord = document.getElementById('vynl-id');
                    vinylRecord.classList.remove('vinyl-animation');
                }

                // Force load the audio
                audioPlayer.load();

                // Handle errors
                audioPlayer.onerror = function() {
                    console.error('Error loading audio file:', track.src);
                    // Try alternative path formats
                    tryAlternativePaths(track);
                };

                // Update song info section
                if (track.infoKey && songDescriptions[track.infoKey]) {
                    // Set the song title in the info box title
                    songInfoTitle.textContent = track.title;
                    songInfoDetails.innerHTML = songDescriptions[track.infoKey];
                    songInfoContainer.classList.add('active');

                    // Make sure the button is visible and properly positioned
                    const startMusicBtn = document.querySelector('.start-music-process-btn');
                    if (startMusicBtn) {
                        startMusicBtn.style.display = 'inline-block';
                        startMusicBtn.innerHTML = '<i class="fas fa-music mr-2"></i>Create Your Own ' + track.title.split(' ')[0] + ' Style Track';
                    }
                } else {
                    // Set the song title in the info box title
                    songInfoTitle.textContent = track.title;
                    songInfoDetails.innerHTML = '<p>No additional information available for this track.</p>';
                    songInfoContainer.classList.add('active');

                    // Default button text
                    const startMusicBtn = document.querySelector('.start-music-process-btn');
                    if (startMusicBtn) {
                        startMusicBtn.style.display = 'inline-block';
                        startMusicBtn.innerHTML = '<i class="fas fa-music mr-2"></i>Start Your Music Journey';
                    }
                }

                // Update library player
                updateLibraryPlayer();
            }

            // Try alternative paths for the audio file
            function tryAlternativePaths(track) {
                // Try different path formats
                const paths = [
                    track.src,
                    track.src.replace(/^\//, ''), // Remove leading slash
                    track.src.replace(/^music\//, '/music/'), // Add leading slash to music/
                    track.src.replace(/^\/music\//, 'music/'), // Remove leading slash from /music/
                    track.src.replace(/ /g, '%20') // Replace spaces with %20
                ];

                // Try each path
                tryNextPath(paths, 0);
            }

            // Try the next path in the list
            function tryNextPath(paths, index) {
                if (index >= paths.length) {
                    console.error('All paths failed');
                    return;
                }

                const path = paths[index];
                console.log('Trying path:', path);

                // Try to load directly
                tryDirectLoad(path, function(success) {
                    if (success) {
                        audioPlayer.src = path;
                        audioPlayer.load();
                        console.log('Successfully loaded:', path);
                    } else {
                        // Try next path
                        tryNextPath(paths, index + 1);
                    }
                });
            }

            // Try to load the file directly
            function tryDirectLoad(url, callback) {
                const xhr = new XMLHttpRequest();
                xhr.open('HEAD', url, true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            callback(true);
                        } else {
                            callback(false);
                        }
                    }
                };
                xhr.send();
            }

            // Play track
            function playTrack() {
                audioPlayer.play().then(() => {
                    // Play started successfully
                    // Update fixed player button
                    playPauseIcon.classList.remove('fa-play');
                    playPauseIcon.classList.add('fa-pause');

                    // Update library player buttons
                    libraryPlayButton.style.display = 'none';
                    libraryPauseButton.style.display = 'block';

                    // Add active class to the play button
                    libraryPlayPauseButton.classList.add('active');

                    // Start vinyl animation
                    const vinylRecord = document.getElementById('vynl-id');
                    vinylRecord.classList.add('vinyl-animation');
                }).catch(error => {
                    console.error('Error playing audio:', error);
                    // Keep the play icon if there was an error
                    playPauseIcon.classList.remove('fa-pause');
                    playPauseIcon.classList.add('fa-play');

                    libraryPlayButton.style.display = 'block';
                    libraryPauseButton.style.display = 'none';

                    // Remove active class
                    libraryPlayPauseButton.classList.remove('active');

                    // Stop vinyl animation
                    const vinylRecord = document.getElementById('vynl-id');
                    vinylRecord.classList.remove('vinyl-animation');
                });
            }

            // Pause track
            function pauseTrack() {
                audioPlayer.pause();

                // Update fixed player button
                playPauseIcon.classList.remove('fa-pause');
                playPauseIcon.classList.add('fa-play');

                // Update library player buttons
                libraryPlayButton.style.display = 'block';
                libraryPauseButton.style.display = 'none';

                // Remove active class
                libraryPlayPauseButton.classList.remove('active');

                // Stop vinyl animation
                const vinylRecord = document.getElementById('vynl-id');
                vinylRecord.classList.remove('vinyl-animation');
            }

            // Format time in MM:SS
            function formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = Math.floor(seconds % 60);
                return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
            }

            // Update progress
            function updateProgress() {
                if (audioPlayer.duration) {
                    const percentage = (audioPlayer.currentTime / audioPlayer.duration) * 100;
                    progressBar.style.width = `${percentage}%`;
                    libraryLoaded.style.width = `${percentage}%`;

                    currentTimeElement.textContent = formatTime(audioPlayer.currentTime);
                    libraryPlayed.textContent = formatTime(audioPlayer.currentTime);
                }
            }

            // Update duration
            function updateDuration() {
                if (audioPlayer.duration && !isNaN(audioPlayer.duration)) {
                    totalTimeElement.textContent = formatTime(audioPlayer.duration);
                    libraryDuration.textContent = formatTime(audioPlayer.duration);

                    // Update track times in the playlist
                    const trackItem = document.querySelector(`.track[data-index="${currentTrackIndex}"]`);
                    if (trackItem) {
                        const timeElement = trackItem.querySelector('time');
                        if (timeElement) {
                            timeElement.textContent = formatTime(audioPlayer.duration);
                        }
                    }
                }
            }

            // Set progress when clicking on progress bar
            function setProgress(e) {
                const width = this.clientWidth;
                const clickX = e.offsetX;
                const duration = audioPlayer.duration;

                if (duration) {
                    audioPlayer.currentTime = (clickX / width) * duration;
                }
            }

            // Update volume UI
            function updateVolumeUI() {
                volumeLevel.style.width = `${audioPlayer.volume * 100}%`;

                if (audioPlayer.volume === 0) {
                    volumeIcon.innerHTML = '<i class="fas fa-volume-mute"></i>';
                } else if (audioPlayer.volume < 0.5) {
                    volumeIcon.innerHTML = '<i class="fas fa-volume-down"></i>';
                } else {
                    volumeIcon.innerHTML = '<i class="fas fa-volume-up"></i>';
                }
            }

            // Set volume when clicking on volume slider
            function setVolume(e) {
                const width = this.clientWidth;
                const clickX = e.offsetX;
                audioPlayer.volume = clickX / width;
                updateVolumeUI();
            }

            // Toggle mute
            function toggleMute() {
                if (audioPlayer.volume > 0) {
                    // Store current volume before muting
                    audioPlayer.dataset.prevVolume = audioPlayer.volume;
                    audioPlayer.volume = 0;
                } else {
                    // Restore previous volume
                    audioPlayer.volume = audioPlayer.dataset.prevVolume || 0.7;
                }
                updateVolumeUI();
            }

            // Toggle music player visibility
            function toggleMusicPlayer() {
                musicPlayerContainer.classList.toggle('active');

                // Change toggle icon
                if (musicPlayerContainer.classList.contains('active')) {
                    musicPlayerToggle.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    musicPlayerToggle.innerHTML = '<i class="fas fa-music"></i>';
                }
            }

            // Play next track
            function playNextTrack() {
                // If loop is active, don't change tracks
                if (isLoopActive) {
                    // Just restart the current track
                    audioPlayer.currentTime = 0;
                    playTrack();
                    return;
                }

                // Move to next track
                currentTrackIndex = (currentTrackIndex + 1) % tracks.length;
                loadTrack(currentTrackIndex);
                playTrack();
            }

            // Play previous track
            function playPrevTrack() {
                // If we're at the beginning of the track, go to previous track
                // Otherwise, restart the current track
                if (audioPlayer.currentTime > 3) {
                    audioPlayer.currentTime = 0;
                } else {
                    currentTrackIndex = (currentTrackIndex - 1 + tracks.length) % tracks.length;
                    loadTrack(currentTrackIndex);
                }
                playTrack();
            }

            // This function is no longer needed but kept for compatibility
            function play(element) {
                if (audioPlayer.paused) {
                    playTrack();
                } else {
                    pauseTrack();
                }
            }

            // Update turntable player UI - no longer needed but kept for compatibility
            function updateTurntableUI() {
                // This function is now empty as we're using the fixed player UI
            }

            // Event Listeners
            musicPlayerToggle.addEventListener('click', toggleMusicPlayer);

            // Connect play/pause button in the fixed player
            playPauseButton.addEventListener('click', function() {
                if (audioPlayer.paused) {
                    playTrack();
                } else {
                    pauseTrack();
                }
            });

            // Connect previous and next buttons
            prevButton.addEventListener('click', playPrevTrack);
            nextButton.addEventListener('click', playNextTrack);

            // Connect volume button
            document.getElementById("volumeIcon").addEventListener('click', toggleMute);

            // Keep the original event listeners for compatibility
            progressContainer.addEventListener('click', setProgress);
            volumeSlider.addEventListener('click', setVolume);

            // Update progress as audio plays
            audioPlayer.addEventListener('timeupdate', function() {
                updateProgress();
            });

            // Update duration when metadata is loaded
            audioPlayer.addEventListener('loadedmetadata', updateDuration);

            // When track ends, play next
            audioPlayer.addEventListener('ended', playNextTrack);

            // Override play and pause functions to update vinyl animation
            const originalPlayTrack = playTrack;
            playTrack = function() {
                originalPlayTrack();

                // Start vinyl animation
                var vinylRecord = document.getElementById("vynl-id");
                if (vinylRecord) {
                    vinylRecord.classList.add("vinyl-animation");
                }
            };

            const originalPauseTrack = pauseTrack;
            pauseTrack = function() {
                originalPauseTrack();

                // Stop vinyl animation
                var vinylRecord = document.getElementById("vynl-id");
                if (vinylRecord) {
                    vinylRecord.classList.remove("vinyl-animation");
                }
            };

            // Initialize player
            initPlayer();
        });
    </script>
</body>
</html>
