document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing player');

    // Elements
    const musicPlayerToggle = document.getElementById('musicPlayerToggle');
    const musicPlayerContainer = document.getElementById('musicPlayerContainer');
    const audioPlayer = document.getElementById('audioPlayer');
    const playPauseButton = document.getElementById('playPauseButton');
    const playPauseIcon = document.getElementById('playPauseIcon');
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('progressBar');
    const currentTimeElement = document.getElementById('currentTime');
    const totalTimeElement = document.getElementById('totalTime');
    const volumeIcon = document.getElementById('volumeIcon');
    const volumeSlider = document.getElementById('volumeSlider');
    const volumeLevel = document.getElementById('volumeLevel');
    const playerAlbumArt = document.getElementById('playerAlbumArt');
    const playerTitle = document.getElementById('playerTitle');
    const playerArtist = document.getElementById('playerArtist');

    // Track list with multiple path options to try
    const tracks = [
        {
            title: "Delia's Melody",
            artist: "Custom Song (2023)",
            // Direct path to audio file
            src: "music/Delia.mp3",
            // Store alternative paths to try if the first one fails
            altPaths: [
                "music/Delia.mp3",
                "./music/Delia.mp3",
                "/music/Delia.mp3",
                "@music/Delia.mp3",
                // Try lowercase versions too
                "music/delia.mp3",
                "./music/delia.mp3",
                "/music/delia.mp3",
                "@music/delia.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1543087903-1ac2ec7aa8c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80"
        },
        {
            title: "Milele Na Wewe",
            artist: "Valentine's Day Song for Stella",
            src: "music/Milele Na Wewe.mp3",
            altPaths: [
                "music/Milele Na Wewe.mp3",
                "./music/Milele Na Wewe.mp3",
                "/music/Milele Na Wewe.mp3",
                "@music/Milele Na Wewe.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1655683576616-c40706fa79f2?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80"
        },
        {
            title: '"Chica Benita" Family Moment',
            artist: "Heartfelt Song for Wife and Children",
            src: "music/ChicaBenita.mp3",
            altPaths: [
                "music/ChicaBenita.mp3",
                "./music/ChicaBenita.mp3",
                "/music/ChicaBenita.mp3",
                "@music/ChicaBenita.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1609220136736-443140cffec6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80"
        },
        {
            title: "Sarah's Song (Version 1)",
            artist: "Celebration Melody",
            src: "music/Sarah 1.mp3",
            altPaths: [
                "music/Sarah 1.mp3",
                "./music/Sarah 1.mp3",
                "/music/Sarah 1.mp3",
                "@music/Sarah 1.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1614291129408-3dd5436942e6?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80"
        },
        {
            title: "Last Man Standing",
            artist: "Victory Anthem",
            src: "music/Last Man Standing.mp3",
            altPaths: [
                "music/Last Man Standing.mp3",
                "./music/Last Man Standing.mp3",
                "/music/Last Man Standing.mp3",
                "@music/Last Man Standing.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1541532713592-79a0317b6b77?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80"
        },
        {
            title: "Hood Love",
            artist: "Street Romance",
            src: "music/Hood Love.mp3",
            altPaths: [
                "music/Hood Love.mp3",
                "./music/Hood Love.mp3",
                "/music/Hood Love.mp3",
                "@music/Hood Love.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1581952976147-5a2d15560349?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80"
        },
        {
            title: "Eagles for Glory",
            artist: "Glorious Heights School Anthem",
            src: "music/Eagles for Glory.mp3",
            altPaths: [
                "music/Eagles for Glory.mp3",
                "./music/Eagles for Glory.mp3",
                "/music/Eagles for Glory.mp3",
                "@music/Eagles for Glory.mp3"
            ],
            albumArt: "https://images.unsplash.com/photo-1686721454934-d874ad6e2ce7?ixlib=rb-4.1.0&auto=format&fit=crop&w=1950&q=80"
        }
    ];

    // Current track index
    let currentTrackIndex = 0;

    // Initialize player
    function initPlayer() {
        // Set initial volume
        audioPlayer.volume = 0.7;
        updateVolumeUI();

        // Debug check for Delia.mp3 file
        fetch('music/Delia.mp3', { method: 'HEAD' })
            .then(response => {
                console.log('Delia.mp3 file exists:', response.ok, 'Status:', response.status);
            })
            .catch(error => {
                console.error('Error checking Delia.mp3:', error);
            });

        // Also try lowercase
        fetch('music/delia.mp3', { method: 'HEAD' })
            .then(response => {
                console.log('delia.mp3 (lowercase) file exists:', response.ok, 'Status:', response.status);
            })
            .catch(error => {
                console.error('Error checking delia.mp3 (lowercase):', error);
            });

        // Load the first track
        loadTrack(currentTrackIndex);

        // Make sure the player is visible since we added the active class to the container
        if (musicPlayerContainer.classList.contains('active')) {
            musicPlayerToggle.innerHTML = '<i class="fas fa-times"></i>';
        }

        console.log('Music player initialized');
    }

    // Current path attempt index for each track
    let currentPathAttempt = 0;

    // Check if a file exists at a given path
    async function checkFileExists(url) {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.log('Error checking file:', url, error);
            return false;
        }
    }

    // Load track
    function loadTrack(index) {
        const track = tracks[index];
        console.log('Loading track:', index, track.title);

        // Remove previous event listeners to prevent duplicates
        audioPlayer.removeEventListener('loadedmetadata', updateDuration);
        audioPlayer.removeEventListener('error', handleAudioError);

        // Set new track info
        // Use a previously successful path if available
        if (track.successfulPath) {
            console.log('Using previously successful path:', track.successfulPath);
            audioPlayer.src = track.successfulPath;
        } else {
            // Try to use the direct path first
            let srcPath = track.src;

            // Handle @ symbol in file paths
            if (srcPath.startsWith('@music/')) {
                srcPath = srcPath.replace('@music/', '/music/');
                console.log('Converted @music path to:', srcPath);
            }

            console.log('Using primary path:', srcPath);
            audioPlayer.src = srcPath;
        }
        playerAlbumArt.src = track.albumArt;
        playerTitle.textContent = track.title;
        playerArtist.textContent = track.artist;

        // Reset progress
        progressBar.style.width = '0%';
        currentTimeElement.textContent = '0:00';
        totalTimeElement.textContent = '0:00';

        // Reset path attempt counter when loading a new track
        currentPathAttempt = 0;

        // Update vinyl animation based on player state
        const vinylRecord = document.getElementById('vynl-id');
        if (vinylRecord) {
            if (!audioPlayer.paused) {
                vinylRecord.classList.add('vinyl-animation');
            } else {
                vinylRecord.classList.remove('vinyl-animation');
            }
        }

        // Add event listeners
        audioPlayer.addEventListener('loadedmetadata', updateDuration);
        audioPlayer.addEventListener('error', (e) => {
            console.log('Error loading audio, trying alternative paths');
            // Try alternative paths if available
            if (track.altPaths && currentPathAttempt < track.altPaths.length) {
                tryNextPath(track, index);
            } else {
                // If all paths failed, show error
                handleAudioError(e);
            }
        });

        // Force load the audio
        console.log('Forcing audio load with src:', audioPlayer.src);
        audioPlayer.load();

        // Log the audio element state
        console.log('Audio element after load:', {
            src: audioPlayer.src,
            paused: audioPlayer.paused,
            readyState: audioPlayer.readyState,
            networkState: audioPlayer.networkState
        });

        // Auto play if player was playing
        if (!audioPlayer.paused) {
            console.log('Auto-playing track');
            audioPlayer.play().then(() => {
                // Update UI on successful play
                playPauseIcon.classList.remove('fa-play');
                playPauseIcon.classList.add('fa-pause');

                // Start vinyl animation
                if (vinylRecord) {
                    vinylRecord.classList.add('vinyl-animation');
                }
            }).catch(e => {
                console.log('Error playing track:', e);
                playPauseIcon.classList.remove('fa-pause');
                playPauseIcon.classList.add('fa-play');

                // Stop vinyl animation
                if (vinylRecord) {
                    vinylRecord.classList.remove('vinyl-animation');
                }
            });
        }
    }

    // Try the next alternative path
    function tryNextPath(track, index) {
        currentPathAttempt++;

        // Check if we've run out of paths
        if (!track.altPaths || currentPathAttempt >= track.altPaths.length) {
            console.log('No more paths to try');
            handleAudioError(new Error('All paths failed'));
            return;
        }

        console.log(`Trying alternative path ${currentPathAttempt}: ${track.altPaths[currentPathAttempt]}`);

        // Remove error listener to prevent infinite loop
        audioPlayer.removeEventListener('error', handleAudioError);

        // Try the next path
        let nextPath = track.altPaths[currentPathAttempt];

        // Check if path is valid
        if (!nextPath) {
            console.log(`Path at index ${currentPathAttempt} is undefined, skipping`);
            tryNextPath(track, index);
            return;
        }

        // Handle @ symbol in file paths
        if (nextPath.startsWith('@music/')) {
            nextPath = nextPath.replace('@music/', '/music/');
            console.log('Converted @music path to:', nextPath);
        }

        audioPlayer.src = nextPath;
        console.log('Set new src:', audioPlayer.src);

        // Add error listener back
        audioPlayer.addEventListener('error', (e) => {
            if (!track.altPaths || currentPathAttempt >= track.altPaths.length) {
                console.log('All paths failed, showing error');
                handleAudioError(e);
                return;
            }

            console.log(`Path ${currentPathAttempt} failed:`, track.altPaths[currentPathAttempt]);
            // Try next path if available
            if (currentPathAttempt < track.altPaths.length - 1) {
                console.log('Trying next path...');
                tryNextPath(track, index);
            } else {
                // If all paths failed, show error
                console.log('All paths failed, showing error');
                handleAudioError(e);
            }
        });

        // Add success listener
        audioPlayer.addEventListener('canplaythrough', () => {
            console.log(`Path ${currentPathAttempt} succeeded:`, track.altPaths[currentPathAttempt]);

            // Store the successful path for future use
            track.successfulPath = nextPath;
        }, { once: true });

        // Force load the audio
        audioPlayer.load();
    }

    // Handle audio error
    function handleAudioError(error) {
        console.error('Audio error:', error);

        // Show error message to user
        playerTitle.textContent = 'Error loading audio';
        playerArtist.textContent = 'Please try another track';

        // Reset play button
        playPauseIcon.classList.remove('fa-pause');
        playPauseIcon.classList.add('fa-play');

        // Stop vinyl animation
        const vinylRecord = document.getElementById('vynl-id');
        if (vinylRecord) {
            vinylRecord.classList.remove('vinyl-animation');
        }

        // Make sure the player is visible so user can see the error
        if (!musicPlayerContainer.classList.contains('active')) {
            toggleMusicPlayer();
        }
    }

    // Update duration when metadata is loaded
    function updateDuration() {
        const duration = audioPlayer.duration;
        totalTimeElement.textContent = formatTime(duration);
    }

    // Format time in MM:SS
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
    }

    // Update progress bar as audio plays
    function updateProgress() {
        const currentTime = audioPlayer.currentTime;
        const duration = audioPlayer.duration;
        if (duration > 0) {
            const progressPercent = (currentTime / duration) * 100;
            progressBar.style.width = `${progressPercent}%`;
            currentTimeElement.textContent = formatTime(currentTime);
        }
    }

    // Set progress when user clicks on progress bar
    function setProgress(e) {
        const width = this.clientWidth;
        const clickX = e.offsetX;
        const duration = audioPlayer.duration;
        audioPlayer.currentTime = (clickX / width) * duration;
    }

    // Set volume when user clicks on volume slider
    function setVolume(e) {
        const width = this.clientWidth;
        const clickX = e.offsetX;
        const volume = clickX / width;
        audioPlayer.volume = volume;
        updateVolumeUI();
    }

    // Update volume UI
    function updateVolumeUI() {
        volumeLevel.style.width = `${audioPlayer.volume * 100}%`;
        if (audioPlayer.volume === 0) {
            volumeIcon.innerHTML = '<i class="fas fa-volume-mute"></i>';
        } else if (audioPlayer.volume < 0.5) {
            volumeIcon.innerHTML = '<i class="fas fa-volume-down"></i>';
        } else {
            volumeIcon.innerHTML = '<i class="fas fa-volume-up"></i>';
        }
    }

    // Toggle mute
    function toggleMute() {
        if (audioPlayer.volume === 0) {
            audioPlayer.volume = 0.7;
        } else {
            audioPlayer.volume = 0;
        }
        updateVolumeUI();
    }

    // Toggle play/pause
    function togglePlayPause() {
        const vinylRecord = document.getElementById('vynl-id');

        if (audioPlayer.paused) {
            // Update UI immediately for better responsiveness
            playPauseIcon.classList.remove('fa-play');
            playPauseIcon.classList.add('fa-pause');

            // Update the corresponding example button
            updateExampleButtonState(currentTrackIndex, true);

            // Start vinyl animation immediately
            if (vinylRecord) {
                vinylRecord.classList.add('vinyl-animation');
            }

            // Then try to play the audio
            audioPlayer.play().then(() => {
                console.log('Audio playing successfully');
            }).catch(error => {
                console.error('Error playing audio:', error);
                // Revert UI changes if play fails
                playPauseIcon.classList.remove('fa-pause');
                playPauseIcon.classList.add('fa-play');

                // Revert example button state
                updateExampleButtonState(currentTrackIndex, false);

                if (vinylRecord) {
                    vinylRecord.classList.remove('vinyl-animation');
                }
            });
        } else {
            // Pause audio
            audioPlayer.pause();

            // Update UI
            playPauseIcon.classList.remove('fa-pause');
            playPauseIcon.classList.add('fa-play');

            // Update the corresponding example button
            updateExampleButtonState(currentTrackIndex, false);

            // Stop vinyl animation
            if (vinylRecord) {
                vinylRecord.classList.remove('vinyl-animation');
            }
        }
    }

    // Helper function to update example button state
    function updateExampleButtonState(trackIndex, isPlaying) {
        // Find all buttons for this track
        const trackButtons = document.querySelectorAll(`.play-example[data-track="${trackIndex}"]`);

        trackButtons.forEach(button => {
            const icon = button.querySelector('i');
            const textSpan = button.querySelector('span');

            if (icon) {
                if (isPlaying) {
                    icon.classList.remove('fa-play');
                    icon.classList.add('fa-pause');
                    if (textSpan) {
                        textSpan.textContent = 'Pause Music Player';
                    }
                } else {
                    icon.classList.remove('fa-pause');
                    icon.classList.add('fa-play');
                    if (textSpan) {
                        textSpan.textContent = 'Play in Music Player';
                    }
                }
            }
        });
    }

    // Play next track
    function playNextTrack() {
        // Reset all example buttons to play state
        document.querySelectorAll('.play-example i').forEach(icon => {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
        });

        // Update to next track
        currentTrackIndex = (currentTrackIndex + 1) % tracks.length;
        loadTrack(currentTrackIndex);

        // Update UI immediately for better responsiveness
        playPauseIcon.classList.remove('fa-play');
        playPauseIcon.classList.add('fa-pause');

        // Update the corresponding example button
        updateExampleButtonState(currentTrackIndex, true);

        // Start vinyl animation immediately
        const vinylRecord = document.getElementById('vynl-id');
        if (vinylRecord) {
            vinylRecord.classList.add('vinyl-animation');
        }

        // Then try to play the audio
        audioPlayer.play().then(() => {
            console.log('Next track playing successfully');
        }).catch(error => {
            console.error('Error playing next track:', error);
            // Revert UI changes if play fails
            playPauseIcon.classList.remove('fa-pause');
            playPauseIcon.classList.add('fa-play');

            // Revert example button state
            updateExampleButtonState(currentTrackIndex, false);

            if (vinylRecord) {
                vinylRecord.classList.remove('vinyl-animation');
            }
        });
    }

    // Play previous track
    function playPrevTrack() {
        // Reset all example buttons to play state
        document.querySelectorAll('.play-example i').forEach(icon => {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
        });

        // Update to previous track
        currentTrackIndex = (currentTrackIndex - 1 + tracks.length) % tracks.length;
        loadTrack(currentTrackIndex);

        // Update UI immediately for better responsiveness
        playPauseIcon.classList.remove('fa-play');
        playPauseIcon.classList.add('fa-pause');

        // Update the corresponding example button
        updateExampleButtonState(currentTrackIndex, true);

        // Start vinyl animation immediately
        const vinylRecord = document.getElementById('vynl-id');
        if (vinylRecord) {
            vinylRecord.classList.add('vinyl-animation');
        }

        // Then try to play the audio
        audioPlayer.play().then(() => {
            console.log('Previous track playing successfully');
        }).catch(error => {
            console.error('Error playing previous track:', error);
            // Revert UI changes if play fails
            playPauseIcon.classList.remove('fa-pause');
            playPauseIcon.classList.add('fa-play');

            // Revert example button state
            updateExampleButtonState(currentTrackIndex, false);

            if (vinylRecord) {
                vinylRecord.classList.remove('vinyl-animation');
            }
        });
    }

    // Toggle music player visibility
    function toggleMusicPlayer() {
        musicPlayerContainer.classList.toggle('active');
        if (musicPlayerContainer.classList.contains('active')) {
            musicPlayerToggle.innerHTML = '<i class="fas fa-times"></i>';
        } else {
            musicPlayerToggle.innerHTML = '<i class="fas fa-music"></i>';
        }
    }

    // Connect example buttons to play specific tracks
    function connectExampleButtons() {
        console.log('Connecting example buttons');
        const exampleButtons = document.querySelectorAll('.play-example');
        console.log('Found example buttons:', exampleButtons.length);

        exampleButtons.forEach((button, idx) => {
            console.log(`Button ${idx}:`, button, 'data-track:', button.getAttribute('data-track'));

            // Remove any existing click listeners (by cloning and replacing the element)
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add click listener
            newButton.addEventListener('click', function() {
                console.log('Example button clicked:', this);
                const trackIndex = parseInt(this.getAttribute('data-track'));
                console.log('Track index:', trackIndex);

                if (!isNaN(trackIndex) && trackIndex >= 0 && trackIndex < tracks.length) {
                    console.log('Valid track index, showing player');
                    // Make sure player is visible
                    if (!musicPlayerContainer.classList.contains('active')) {
                        toggleMusicPlayer();
                    }

                    // Check if we're already playing this track
                    if (currentTrackIndex === trackIndex && !audioPlayer.paused) {
                        // If the same track is already playing, pause it
                        audioPlayer.pause();

                        // Update UI
                        playPauseIcon.classList.remove('fa-pause');
                        playPauseIcon.classList.add('fa-play');

                        // Update button icon and text
                        const buttonIcon = this.querySelector('i');
                        const buttonText = this.querySelector('span');

                        if (buttonIcon) {
                            buttonIcon.classList.remove('fa-pause');
                            buttonIcon.classList.add('fa-play');
                        }

                        if (buttonText) {
                            buttonText.textContent = 'Play in Music Player';
                        }

                        // Stop vinyl animation
                        const vinylRecord = document.getElementById('vynl-id');
                        if (vinylRecord) {
                            vinylRecord.classList.remove('vinyl-animation');
                        }

                        console.log('Paused current track');
                        return;
                    }

                    // If we're playing a different track or the current track is paused

                    // Update all example buttons to show play icon
                    document.querySelectorAll('.play-example i').forEach(icon => {
                        icon.classList.remove('fa-pause');
                        icon.classList.add('fa-play');
                    });

                    // Update this button to show pause icon and text
                    const buttonIcon = this.querySelector('i');
                    const buttonText = this.querySelector('span');

                    if (buttonIcon) {
                        buttonIcon.classList.remove('fa-play');
                        buttonIcon.classList.add('fa-pause');
                    }

                    if (buttonText) {
                        buttonText.textContent = 'Pause Music Player';
                    }

                    // Load the track if it's different from current
                    if (currentTrackIndex !== trackIndex) {
                        currentTrackIndex = trackIndex;
                        loadTrack(currentTrackIndex);
                    }

                    // Update play/pause button to show pause icon
                    playPauseIcon.classList.remove('fa-play');
                    playPauseIcon.classList.add('fa-pause');

                    // Start vinyl animation
                    const vinylRecord = document.getElementById('vynl-id');
                    if (vinylRecord) {
                        vinylRecord.classList.add('vinyl-animation');
                    }

                    // Try to play immediately
                    audioPlayer.play().then(() => {
                        console.log('Direct load successful');
                    }).catch((error) => {
                        // If direct load fails, fall back to normal loading
                        console.log('Falling back to normal loading:', error);

                        // Force a complete reload of the track
                        audioPlayer.src = '';
                        setTimeout(() => {
                            // Load the track with a fresh state
                            loadTrack(currentTrackIndex);

                            // Ensure it plays with error handling
                            setTimeout(() => {
                                audioPlayer.play().then(() => {
                                    // Play started successfully
                                    console.log('Track playing successfully');
                                    playPauseIcon.classList.remove('fa-play');
                                    playPauseIcon.classList.add('fa-pause');

                                    // Start vinyl animation
                                    if (vinylRecord) {
                                        vinylRecord.classList.add('vinyl-animation');
                                    }

                                    // Update this button to show pause icon and text
                                    const buttonIcon = this.querySelector('i');
                                    const buttonTextSpan = this.querySelector('span');

                                    if (buttonIcon) {
                                        buttonIcon.classList.remove('fa-play');
                                        buttonIcon.classList.add('fa-pause');
                                    }

                                    if (buttonTextSpan) {
                                        buttonTextSpan.textContent = 'Pause Music Player';
                                    }
                                }).catch(error => {
                                    console.error('Error playing audio from example button:', error);
                                    // Keep the play icon if there was an error
                                    playPauseIcon.classList.remove('fa-pause');
                                    playPauseIcon.classList.add('fa-play');

                                    // Reset button icon and text
                                    const buttonIcon = this.querySelector('i');
                                    const buttonTextSpan = this.querySelector('span');

                                    if (buttonIcon) {
                                        buttonIcon.classList.remove('fa-pause');
                                        buttonIcon.classList.add('fa-play');
                                    }

                                    if (buttonTextSpan) {
                                        buttonTextSpan.textContent = 'Play in Music Player';
                                    }

                                    // Stop vinyl animation
                                    if (vinylRecord) {
                                        vinylRecord.classList.remove('vinyl-animation');
                                    }
                                });
                            }, 100);
                        }, 100);
                    });
                }
            });
        });
    }

    // Event Listeners
    if (musicPlayerToggle) musicPlayerToggle.addEventListener('click', toggleMusicPlayer);
    if (playPauseButton) playPauseButton.addEventListener('click', togglePlayPause);
    if (prevButton) prevButton.addEventListener('click', playPrevTrack);
    if (nextButton) nextButton.addEventListener('click', playNextTrack);
    if (progressContainer) progressContainer.addEventListener('click', setProgress);
    if (volumeSlider) volumeSlider.addEventListener('click', setVolume);
    if (volumeIcon) volumeIcon.addEventListener('click', toggleMute);

    // Update progress as audio plays
    if (audioPlayer) {
        audioPlayer.addEventListener('timeupdate', updateProgress);
        // When track ends, play next
        audioPlayer.addEventListener('ended', playNextTrack);
    }

    // Initialize player
    initPlayer();

    // Connect example buttons immediately
    connectExampleButtons();

    // Also connect them after a delay to ensure all DOM elements are fully loaded
    // This provides a fallback in case some elements weren't ready initially
    setTimeout(connectExampleButtons, 1000);
});
