Product Requirements Document (PRD): Aifrobeats.com 2.0

Version: 1.1
Date: July 5, 2025
Author: AI Assistant
Project: Transition Aifrobeats.com from a static HTML/CSS site to a dynamic, freemium web application using Firebase.

1. Introduction & Vision

Aifrobeats.com is evolving from a simple, static service page into a scalable, user-centric platform. The core business model is shifting to a freemium system to increase user sign-ups, demonstrate value, and drive sales conversions.

The new platform's value proposition is: "AI speed, human touch." We leverage AI for rapid music generation, with the indispensable final step of human producer vetting, mastering, and cleanup to deliver a premium product. This PRD outlines the requirements to build this dynamic application.

2. Target User Personas

The New User (Indie Creator/Hobbyist): Wants to try the service with zero financial risk. Needs to be convinced of the quality before committing. Their goal is to get a custom track for a personal project.

The Paying Customer (Content Creator/Business): Values their time and needs high-quality, commercially-licensed music for their projects (YouTube, TikTok, podcasts, jingles). They are willing to pay for a reliable, fast service that delivers.

The Administrator (You): Needs a simple interface to view and manage incoming song requests, and to upload the finished, watermarked previews.

3. Core Features & Functionality
3.1. User Account System (Firebase Authentication)

User Story: As a new user, I want to create a free account so I can receive my free song credits and manage my music.

Requirements:

Sign-Up Page:
- Input fields: Email, Password.
- Implement using Firebase Authentication SDK.
- On successful sign-up, a new user document is created in Firestore "users" collection.
- Crucial Logic: Upon creation, each new user document must be automatically allocated 3 Free Preview Credits.

Login Page:
- Input fields: Email, Password.
- Implement using Firebase Authentication SDK.
- Secure authentication process handled by Firebase.

User Dashboard:
- This is the private, logged-in area for users.
- It must display the user's current Preview Credit balance from Firestore.
- It must contain a gallery/list of all their requested song previews.

3.2. The Credit & Preview System (Firestore)

User Story: As a user, I want to use credits to request watermarked song previews so I can test the service before I buy.

Database Schema (Firestore):

"users" collection:
- Document ID: Firebase Auth UID
- Fields: email, previewCredits, createdAt, etc.

Core Logic:
- Credit Allocation: New users start with previewCredits = 3.
- Credit Deduction Workflow: When a user submits a song request:
  - The system must first check if currentUser.previewCredits > 0.
  - If YES:
    - Subtract 1 from currentUser.previewCredits in Firestore.
    - Submit the song request to the "requests" collection.
    - Display a success message to the user.
  - If NO:
    - Block the song request submission.
    - Display a message: "You are out of credits. Please purchase more to continue."
    - Show a button to "Buy More Credits."

3.3. Song Request & Delivery Workflow (Firestore + Firebase Storage)

User Story: As a user, I want to easily describe the song I want and receive a preview in my dashboard.

Requirements:

Request Form: A clean form (can be on a dedicated page or within the dashboard) with fields for "Song Type," "Description," etc.

Database Schema:
"requests" collection:
- Document ID: Auto-generated
- Fields: userId, songType, description, status, createdAt, previewUrl, etc.

Admin Dashboard: A simple backend view for the Administrator to see a list of new song requests, sorted by date.

Song Delivery: 
- The Administrator, after creating the watermarked song, will upload it to Firebase Storage.
- The system must update the corresponding request document with the previewUrl.
- The audio file will appear in the User Dashboard with a "Play" button.
- The audio must contain a recurring watermark (e.g., a voice saying "Aifrobeats.com").

3.4. Monetization (Firebase + Stripe)

User Story: As a user, I want to purchase the full, clean version of a song I like, or buy more credits to create more previews.

Requirements:

Primary Monetization - Unlocking a Full Track:
- In the User Dashboard, next to each watermarked song preview, there must be a "Buy Full Version - $10" button.
- This button initiates a payment workflow via Stripe.
- Upon successful payment, the system updates the request document with isPurchased=true and fullVersionUrl.
- The user gains access to download the high-quality, non-watermarked audio file.

Secondary Monetization - Purchasing Preview Credits:
- Create a simple purchasing page where users can buy more credits.
- Example packages: "5 Credits for $3," "10 Credits for $5."
- Upon successful payment, the system adds the purchased amount to the currentUser.previewCredits field in Firestore.

4. UI/UX Homepage Redesign

The current homepage needs to be restructured to support the new user journey.

Header/Navigation Bar:
- Add "Login" and "Sign Up" buttons for logged-out users.
- The "Sign Up" button should be the primary call-to-action (CTA).
- For logged-in users, these should be replaced with "My Dashboard" and "Logout" links.
- Use Firebase Auth state listeners to dynamically update the navigation.

Hero Section:
- Change the headline to focus on the freemium offer: "Get 3 Free Custom Afrobeats Previews."
- The primary button should be "Create My Free Song," which links directly to the Sign-Up page.

Remove the Main Order Form: The long form on the homepage should be removed. The request process begins after sign-up.

"How It Works" Section:
- Rewrite to explain the new 3-step process: 1. Sign Up & Request. 2. Get Your Watermarked Previews. 3. Buy the Ones You Love.

Pricing Section:
- Create a simple table comparing the Free Account (3 free previews, watermarked) vs. the Premium Track ($10, watermark-free, commercial rights).

5. Technology & Implementation Requirements

Firebase Services Required:
- Firebase Authentication: For user sign-up, login, and session management.
- Firestore Database: For storing user data, song requests, and transaction records.
- Firebase Storage: For storing audio files (both watermarked previews and full versions).
- Firebase Hosting: For deploying the web application.

Security:
- Implement proper Firestore security rules to protect user data.
- Set up Firebase Storage rules to control access to audio files.
- Use Firebase Authentication for secure user management.

Payment Gateway:
- Integrate Stripe for payment processing.
- Implement webhook handlers for payment confirmation.

This PRD provides a comprehensive, step-by-step direction for building Aifrobeats.com 2.0 using Firebase services. The implementation leverages Firebase's built-in authentication, database, and storage capabilities to create a scalable, secure freemium web application.
