<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Custom Song - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">

    <style>
        /* Enhanced Request Form Styles */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.95rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
            letter-spacing: 0.025em;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
        }

        @media (max-width: 768px) {
            .form-row, .form-row-3 {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f3f4f6;
        }

        .step-number {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.25rem;
            margin-right: 1rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .step-title {
            flex: 1;
        }

        .step-title h2 {
            font-size: 1.75rem;
            font-weight: bold;
            color: #1f2937;
            margin: 0 0 0.25rem 0;
        }

        .step-subtitle {
            color: #6b7280;
            font-size: 1rem;
            margin: 0;
        }

        .form-step {
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.5s ease;
            display: none;
        }

        .form-step.active {
            opacity: 1;
            transform: translateX(0);
            display: block;
        }

        .progress-container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669, #3b82f6);
            border-radius: 4px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .progress-step.active {
            color: #10b981;
            font-weight: 600;
        }

        .insufficient-credits-warning {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(239, 68, 68, 0.3);
        }

        .nav-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #f3f4f6;
        }

        .btn {
            padding: 0.875rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
        }

        .btn-submit {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            color: white;
            padding: 1.25rem 3rem;
            font-size: 1.125rem;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.5);
        }

        .summary-card {
            background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
            border: 2px solid #bbf7d0;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .cost-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .field-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 1.125rem;
        }

        .field-with-icon {
            position: relative;
        }

        .field-with-icon .form-input {
            padding-left: 3rem;
        }

        .checkbox-group {
            background: #f9fafb;
            border-radius: 12px;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .checkbox-group:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }
    </style>
</head>
<body class="min-h-screen" style="background-color: var(--bg-primary);">
    
    <!-- Navigation Header -->
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg);">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                    <i class="fas fa-coins text-green-600"></i>
                    <span id="user-credits" class="font-semibold text-green-700">0</span>
                    <span class="text-green-600 text-sm">credits</span>
                </div>
                <a href="dashboard.html" class="text-gray-600 hover:text-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Dashboard
                </a>
                <button id="logout-btn" class="text-red-600 hover:text-red-700 transition-colors">
                    <i class="fas fa-sign-out-alt mr-1"></i>Logout
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">

        <!-- Enhanced Header -->
        <div class="text-center mb-12">
            <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-3xl p-8 text-white shadow-2xl mb-8">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-music text-3xl"></i>
                    </div>
                    <div class="text-left">
                        <h1 class="text-4xl font-bold mb-2">Create Your Custom Song</h1>
                        <p class="text-green-100 text-lg">Tell us your vision, we'll make it reality</p>
                    </div>
                </div>

                <!-- Credit Status -->
                <div class="bg-white bg-opacity-20 rounded-xl p-4 mt-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins text-yellow-300 text-2xl mr-3"></i>
                            <div>
                                <div class="text-sm text-green-100">Available Credits</div>
                                <div class="text-2xl font-bold" id="header-credits">0</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-green-100">Cost per Request</div>
                            <div class="text-2xl font-bold">1 Credit</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Credit Check Warning -->
        <div id="insufficient-credits" class="hidden insufficient-credits-warning mb-8">
            <div class="flex items-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-exclamation-triangle text-2xl"></i>
                </div>
                <div class="flex-1">
                    <h3 class="text-xl font-bold mb-2">Insufficient Credits</h3>
                    <p class="text-white text-opacity-90 mb-4">You need at least 1 credit to request a custom song. Purchase more credits to continue creating amazing music!</p>
                    <button onclick="buyCredits()" class="bg-white text-red-600 font-semibold px-6 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-shopping-cart mr-2"></i>Buy Credits Now
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Progress Indicator -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill" style="width: 33.33%"></div>
                </div>
                <div class="progress-steps">
                    <div class="progress-step active" id="progress-step-1">
                        <i class="fas fa-music"></i>
                        <span>Song Details</span>
                    </div>
                    <div class="progress-step" id="progress-step-2">
                        <i class="fas fa-envelope"></i>
                        <span>Contact Info</span>
                    </div>
                    <div class="progress-step" id="progress-step-3">
                        <i class="fas fa-check-circle"></i>
                        <span>Review & Submit</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Request Form -->
        <div class="max-w-4xl mx-auto">
            <form id="request-form" class="space-y-8">

                <!-- Step 1: Song Details -->
                <div class="form-section form-step active" id="step-1">
                    <div class="step-header">
                        <div class="step-number">1</div>
                        <div class="step-title">
                            <h2>Song Details & Creative Vision</h2>
                            <p class="step-subtitle">Tell us about your perfect song - the more details, the better!</p>
                        </div>
                    </div>

                    <!-- Basic Song Information -->
                    <div class="form-row">
                        <!-- Song Title -->
                        <div class="form-group">
                            <label for="song-title" class="form-label">
                                <i class="fas fa-music mr-2 text-green-500"></i>
                                Song Title <span class="text-red-500">*</span>
                            </label>
                            <div class="field-with-icon">
                                <i class="field-icon fas fa-heading"></i>
                                <input type="text" id="song-title" name="songTitle" required
                                       class="form-input"
                                       placeholder="e.g., 'Sarah's Wedding Dance', 'Birthday Celebration'">
                            </div>
                        </div>

                        <!-- Song Type -->
                        <div class="form-group">
                            <label for="song-type" class="form-label">
                                <i class="fas fa-tags mr-2 text-blue-500"></i>
                                Song Type <span class="text-red-500">*</span>
                            </label>
                            <select id="song-type" name="songType" required class="form-select">
                                <option value="">Choose your song category...</option>
                                <option value="wedding">💒 Wedding Song</option>
                                <option value="birthday">🎂 Birthday Celebration</option>
                                <option value="love-song">💕 Love Song</option>
                                <option value="jingle">📢 Business Jingle</option>
                                <option value="fitness">💪 Fitness/Workout</option>
                                <option value="corporate">🏢 Corporate Event</option>
                                <option value="club">🎉 Club Anthem</option>
                                <option value="graduation">🎓 Graduation Song</option>
                                <option value="anniversary">💍 Anniversary</option>
                                <option value="custom">✨ Custom/Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Song Vision -->
                    <div class="form-group">
                        <label for="description" class="form-label">
                            <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                            Describe Your Vision <span class="text-red-500">*</span>
                        </label>
                        <textarea id="description" name="description" required rows="5"
                                  class="form-textarea"
                                  placeholder="Tell us everything! Include:
• The occasion or event
• Names to include in the song
• Specific lyrics or phrases you want
• The mood you're going for
• Any cultural references
• Special memories to mention

Example: 'This is for my sister Sarah's wedding to Michael. They met at a coffee shop in Lagos. I want it to be upbeat and joyful, mentioning their love for dancing and how they make each other laugh...'"></textarea>
                        <div class="flex items-center mt-3 text-sm text-gray-600 bg-yellow-50 p-3 rounded-lg">
                            <i class="fas fa-star text-yellow-500 mr-2"></i>
                            <span><strong>Pro Tip:</strong> The more details you provide, the more personalized and amazing your song will be!</span>
                        </div>
                    </div>

                    <!-- Style & Preferences -->
                    <div class="form-row-3">
                        <!-- Mood/Style -->
                        <div class="form-group">
                            <label for="mood" class="form-label">
                                <i class="fas fa-palette mr-2 text-purple-500"></i>
                                Mood & Style
                            </label>
                            <select id="mood" name="mood" class="form-select">
                                <option value="">Select mood...</option>
                                <option value="upbeat">🚀 Upbeat & Energetic</option>
                                <option value="romantic">💖 Romantic & Smooth</option>
                                <option value="celebratory">🎊 Celebratory & Joyful</option>
                                <option value="motivational">💪 Motivational & Inspiring</option>
                                <option value="chill">😌 Chill & Relaxed</option>
                                <option value="party">🎉 Party & Dance</option>
                                <option value="emotional">😢 Emotional & Heartfelt</option>
                                <option value="fun">😄 Fun & Playful</option>
                            </select>
                        </div>

                        <!-- Duration -->
                        <div class="form-group">
                            <label for="duration" class="form-label">
                                <i class="fas fa-clock mr-2 text-indigo-500"></i>
                                Song Duration
                            </label>
                            <select id="duration" name="duration" class="form-select">
                                <option value="">Select length...</option>
                                <option value="30-60">⚡ 30-60 seconds (Quick & Punchy)</option>
                                <option value="60-120">🎵 1-2 minutes (Standard)</option>
                                <option value="120-180">🎶 2-3 minutes (Full Song)</option>
                                <option value="180+">🎼 3+ minutes (Extended)</option>
                                <option value="custom">📏 Custom length</option>
                            </select>
                        </div>

                        <!-- Language -->
                        <div class="form-group">
                            <label for="language" class="form-label">
                                <i class="fas fa-globe mr-2 text-green-500"></i>
                                Primary Language
                            </label>
                            <select id="language" name="language" class="form-select">
                                <option value="">Select language...</option>
                                <option value="english">🇺🇸 English</option>
                                <option value="pidgin">🇳🇬 Nigerian Pidgin</option>
                                <option value="yoruba">🇳🇬 Yoruba</option>
                                <option value="igbo">🇳🇬 Igbo</option>
                                <option value="hausa">🇳🇬 Hausa</option>
                                <option value="mixed">🌍 Mixed Languages</option>
                                <option value="other">🗣️ Other (specify in instructions)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Special Instructions -->
                    <div class="form-group">
                        <label for="special-instructions" class="form-label">
                            <i class="fas fa-magic mr-2 text-pink-500"></i>
                            Special Instructions & Requests
                        </label>
                        <textarea id="special-instructions" name="specialInstructions" rows="4"
                                  class="form-textarea"
                                  placeholder="Any additional requests:
• Specific instruments you want featured
• Names to avoid or include
• Cultural references or traditions
• Tempo preferences (fast, slow, medium)
• Any lyrics or phrases to avoid
• Special dedications or messages
• Inspiration from other songs (but we'll make it unique!)"></textarea>
                    </div>
                </div>

                <!-- Step 2: Contact Information -->
                <div class="form-section form-step" id="step-2">
                    <div class="step-header">
                        <div class="step-number">2</div>
                        <div class="step-title">
                            <h2>Contact Information</h2>
                            <p class="step-subtitle">We'll notify you when your song is ready in your dashboard</p>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-group">
                        <label for="contact-email" class="form-label">
                            <i class="fas fa-envelope mr-2 text-blue-500"></i>
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <div class="field-with-icon">
                            <i class="field-icon fas fa-envelope"></i>
                            <input type="email" id="contact-email" name="contactEmail" required
                                   class="form-input"
                                   placeholder="e.g., <EMAIL>">
                        </div>
                        <p class="text-sm text-gray-600 mt-2 flex items-center">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>We'll notify you when your song is ready in your dashboard</span>
                        </p>
                    </div>

                    <!-- Dashboard Access Info -->
                    <div class="form-group">
                        <div class="bg-green-50 border border-green-200 rounded-xl p-4">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-green-600 text-xl mr-3"></i>
                                <h4 class="font-semibold text-green-800">Your Song Will Be Available in Your Dashboard</h4>
                            </div>
                            <div class="text-sm text-green-700 space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span>Download your completed song anytime from your dashboard</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span>Track the progress of your request in real-time</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span>Get email notifications when your song is ready</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span>Access both MP3 and high-quality WAV formats</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Review & Submit -->
                <div class="form-section form-step" id="step-3">
                    <div class="step-header">
                        <div class="step-number">3</div>
                        <div class="step-title">
                            <h2>Review Your Request & Submit</h2>
                            <p class="step-subtitle">Double-check everything looks perfect before we start creating your song!</p>
                        </div>
                    </div>

                    <!-- Request Summary -->
                    <div class="summary-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-list-check mr-3 text-green-600"></i>
                            Your Song Request Summary
                        </h3>
                        <div id="request-summary" class="space-y-3">
                            <!-- Summary will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Timeline & Process -->
                    <div class="cost-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-600"></i>
                            What Happens Next?
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4">1</div>
                                <div>
                                    <div class="font-semibold text-gray-800">Request Received</div>
                                    <div class="text-sm text-gray-600">We'll confirm your request immediately</div>
                                </div>
                                <div class="ml-auto text-sm text-blue-600 font-medium">Instant</div>
                            </div>
                            <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                                <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4">2</div>
                                <div>
                                    <div class="font-semibold text-gray-800">Preview Ready</div>
                                    <div class="text-sm text-gray-600">First draft for your feedback</div>
                                </div>
                                <div class="ml-auto text-sm text-yellow-600 font-medium">30-60 mins</div>
                            </div>
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4">3</div>
                                <div>
                                    <div class="font-semibold text-gray-800">Song Available in Dashboard</div>
                                    <div class="text-sm text-gray-600">Download your polished, mastered song anytime</div>
                                </div>
                                <div class="ml-auto text-sm text-green-600 font-medium">1-3 hours</div>
                            </div>
                        </div>
                    </div>

                    <!-- Cost Breakdown -->
                    <div class="cost-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-receipt mr-3 text-purple-600"></i>
                            Cost Breakdown
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-music text-green-500 mr-3"></i>
                                    <span class="font-medium">Custom Afrobeats Song</span>
                                </div>
                                <span class="font-semibold text-lg">1 Credit</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-headphones text-blue-500 mr-3"></i>
                                    <span class="font-medium">Professional Production</span>
                                </div>
                                <span class="text-green-600 font-medium">Included</span>
                            </div>
                            <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-tachometer-alt text-purple-500 mr-3"></i>
                                    <span class="font-medium">Dashboard Access & Download</span>
                                </div>
                                <span class="text-green-600 font-medium">Included</span>
                            </div>
                            <div class="flex justify-between items-center py-3 bg-green-50 rounded-lg px-4 mt-4">
                                <span class="font-bold text-lg text-gray-800">Total Cost</span>
                                <span class="font-bold text-2xl text-green-600">1 Credit</span>
                            </div>
                        </div>
                    </div>

                    <!-- Terms Agreement -->
                    <div class="cost-card">
                        <div class="flex items-start">
                            <input type="checkbox" id="terms-agreement" name="termsAgreement" required class="mr-3 mt-1 w-4 h-4 text-green-600">
                            <label for="terms-agreement" class="text-sm text-gray-700">
                                I agree to the <a href="terms.html" target="_blank" class="text-green-600 hover:text-green-700 underline">Terms of Service</a>
                                and <a href="privacy.html" target="_blank" class="text-green-600 hover:text-green-700 underline">Privacy Policy</a>.
                                I understand that this is a custom creation and may take 1-3 hours to complete. <span class="text-red-500">*</span>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" id="submit-btn" class="btn btn-submit">
                            <i class="fas fa-rocket mr-3"></i>
                            Submit My Song Request (1 Credit)
                        </button>
                        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-shield-check text-green-500 mr-2"></i>
                                <span>100% Satisfaction Guaranteed</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-clock text-blue-500 mr-2"></i>
                                <span>Fast 1-3 Hour Delivery</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-heart text-red-500 mr-2"></i>
                                <span>Made with Love in Nigeria</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Form Navigation -->
                <div class="nav-buttons">
                    <button type="button" id="prev-btn" class="btn btn-secondary hidden">
                        <i class="fas fa-arrow-left"></i>
                        Previous Step
                    </button>
                    <div class="flex-1"></div>
                    <button type="button" id="next-btn" class="btn btn-primary">
                        Next Step
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </form>
        </div>
    </main>

    <!-- Footer -->
    <footer id="contact" class="site-footer bg-gray-900 text-gray-400 py-12 md:py-16">
        <div class="container mx-auto px-4 md:px-6 footer-container">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-10">
                <!-- Company Info -->
                <div class="footer-col">
                    <h3 class="text-xl md:text-2xl font-semibold text-white mb-4 flex items-center">
                        <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-2">
                        <span class="animated-gradient-text">Aifrobeats</span>
                    </h3>
                    <p class="mb-4 text-sm md:text-base">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-3">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:<EMAIL>" class="hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-3"></i>
                        <a href="tel:+2347038808350" class="hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Quick Links</h3>
                    <ul class="space-y-2 footer-links">
                        <li>
                            <a href="index.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="dashboard.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="request-form.html" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Request Song
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div class="footer-col">
                    <h3 class="text-lg md:text-xl font-semibold text-white mb-4">Connect With Us</h3>
                    <p class="mb-3 text-sm md:text-base">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon w-8 h-8 md:w-9 md:h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg newsletter-form">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="form-input bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-6 md:mt-8 pt-5 md:pt-6 border-t border-gray-700 text-center text-xs md:text-sm">
                <p>&copy; <span id="currentYear"></span> AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline">|</span>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Success Modal -->
    <div id="success-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">Request Submitted!</h3>
            <p class="text-gray-600 mb-4">Your custom song request has been submitted successfully. We'll have your track ready in 1-3 hours.</p>
            <div class="space-y-3">
                <a href="dashboard.html" class="primary-button w-full">
                    <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                </a>
                <button onclick="closeSuccessModal()" class="secondary-button w-full">
                    Create Another Request
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Submitting your request...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>

    <!-- Request Form Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "1013326896271",
            appId: "1:1013326896271:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        let currentUser = null;
        let userCredits = 0;
        let currentStep = 1;
        const totalSteps = 3;

        // Auth state observer
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                currentUser = user;
                await loadUserData();
                initializeForm();
            } else {
                // User is not signed in, redirect to login
                window.location.href = 'login.html';
            }
        });

        // Enhanced real-time user data loading
        function loadUserData() {
            if (!currentUser) return;

            // Set up real-time listener for user data
            const userDocRef = db.collection('users').doc(currentUser.uid);
            userDocRef.onSnapshot(async (doc) => {
                try {
                    if (doc.exists) {
                        const userData = doc.data();
                        userCredits = userData.credits || 0;

                        // Update multiple credit displays
                        document.getElementById('user-credits').textContent = userCredits;
                        document.getElementById('header-credits').textContent = userCredits;

                        // Check if user has sufficient credits
                        checkCreditAvailability();

                        console.log('User credits updated:', userCredits);
                    } else {
                        console.log('User document does not exist, creating...');
                        // Create user document with 3 credits if it doesn't exist
                        await userDocRef.set({
                            email: currentUser.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp()
                        });
                    }
                } catch (error) {
                    console.error('Error in user data snapshot:', error);
                }
            }, (error) => {
                console.error('Error loading user data:', error);
                alert('Error loading your account data. Please refresh the page.');
            });
        }

        // Check credit availability
        function checkCreditAvailability() {
            const insufficientCreditsDiv = document.getElementById('insufficient-credits');
            const submitBtn = document.getElementById('submit-btn');
            const nextBtn = document.getElementById('next-btn');

            if (userCredits < 1) {
                insufficientCreditsDiv.classList.remove('hidden');
                submitBtn.disabled = true;
                submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                if (currentStep === totalSteps) {
                    nextBtn.disabled = true;
                    nextBtn.classList.add('opacity-50', 'cursor-not-allowed');
                }
            } else {
                insufficientCreditsDiv.classList.add('hidden');
                submitBtn.disabled = false;
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                nextBtn.disabled = false;
                nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }

        // Initialize form functionality
        function initializeForm() {
            updateProgressBar();
            updateStepVisibility();
            setupFormNavigation();
            setupFormValidation();
        }

        // Multi-step form navigation
        function setupFormNavigation() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');

            nextBtn.addEventListener('click', () => {
                if (validateCurrentStep()) {
                    if (currentStep < totalSteps) {
                        currentStep++;
                        updateStepVisibility();
                        updateProgressBar();
                        updateNavigationButtons();
                        if (currentStep === totalSteps) {
                            updateRequestSummary();
                        }
                    }
                }
            });

            prevBtn.addEventListener('click', () => {
                if (currentStep > 1) {
                    currentStep--;
                    updateStepVisibility();
                    updateProgressBar();
                    updateNavigationButtons();
                }
            });
        }

        // Update step visibility
        function updateStepVisibility() {
            for (let i = 1; i <= totalSteps; i++) {
                const step = document.getElementById(`step-${i}`);
                if (i === currentStep) {
                    step.classList.add('active');
                    step.classList.remove('hidden');
                } else {
                    step.classList.remove('active');
                    step.classList.add('hidden');
                }
            }
        }

        // Update progress bar and step indicators
        function updateProgressBar() {
            const progressFill = document.getElementById('progress-fill');
            const progress = (currentStep / totalSteps) * 100;
            progressFill.style.width = `${progress}%`;

            // Update step indicators
            for (let i = 1; i <= totalSteps; i++) {
                const stepIndicator = document.getElementById(`progress-step-${i}`);
                if (stepIndicator) {
                    if (i <= currentStep) {
                        stepIndicator.classList.add('active');
                    } else {
                        stepIndicator.classList.remove('active');
                    }
                }
            }
        }

        // Update navigation buttons
        function updateNavigationButtons() {
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');

            // Show/hide previous button
            if (currentStep === 1) {
                prevBtn.classList.add('hidden');
            } else {
                prevBtn.classList.remove('hidden');
            }

            // Update next button text and visibility
            if (currentStep === totalSteps) {
                nextBtn.classList.add('hidden');
            } else {
                nextBtn.classList.remove('hidden');
                nextBtn.innerHTML = `Next <i class="fas fa-arrow-right ml-2"></i>`;
            }
        }

        // Validate current step
        function validateCurrentStep() {
            const currentStepElement = document.getElementById(`step-${currentStep}`);
            const requiredFields = currentStepElement.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            if (!isValid) {
                alert('Please fill in all required fields before proceeding.');
            }

            return isValid;
        }



        // Setup form validation
        function setupFormValidation() {
            const form = document.getElementById('request-form');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (input.hasAttribute('required') && !input.value.trim()) {
                        input.classList.add('border-red-500');
                    } else {
                        input.classList.remove('border-red-500');
                    }
                });

                input.addEventListener('input', () => {
                    if (input.classList.contains('border-red-500') && input.value.trim()) {
                        input.classList.remove('border-red-500');
                    }
                });
            });
        }

        // Update request summary
        function updateRequestSummary() {
            const summaryDiv = document.getElementById('request-summary');
            const formData = new FormData(document.getElementById('request-form'));

            const getSelectText = (selectId) => {
                const select = document.getElementById(selectId);
                return select.options[select.selectedIndex]?.text || 'Not specified';
            };

            const summary = [
                {
                    icon: 'fas fa-music text-green-500',
                    label: 'Song Title',
                    value: formData.get('songTitle') || 'Not specified'
                },
                {
                    icon: 'fas fa-tags text-blue-500',
                    label: 'Song Type',
                    value: getSelectText('song-type')
                },
                {
                    icon: 'fas fa-palette text-purple-500',
                    label: 'Mood & Style',
                    value: getSelectText('mood')
                },
                {
                    icon: 'fas fa-clock text-indigo-500',
                    label: 'Duration',
                    value: getSelectText('duration')
                },
                {
                    icon: 'fas fa-globe text-green-500',
                    label: 'Language',
                    value: getSelectText('language')
                },
                {
                    icon: 'fas fa-envelope text-blue-500',
                    label: 'Email Address',
                    value: formData.get('contactEmail') || 'Not specified'
                }
            ];

            const description = formData.get('description');
            const specialInstructions = formData.get('specialInstructions');

            summaryDiv.innerHTML = `
                <div class="grid md:grid-cols-2 gap-4 mb-4">
                    ${summary.map(item =>
                        `<div class="flex items-center py-2">
                            <i class="${item.icon} mr-3"></i>
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">${item.label}</div>
                                <div class="text-sm text-gray-600">${item.value}</div>
                            </div>
                        </div>`
                    ).join('')}
                </div>

                ${description ? `
                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <div class="flex items-start">
                            <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i>
                            <div class="flex-1">
                                <div class="font-medium text-gray-800 mb-2">Your Vision</div>
                                <div class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">${description}</div>
                            </div>
                        </div>
                    </div>
                ` : ''}

                ${specialInstructions ? `
                    <div class="border-t border-gray-200 pt-4">
                        <div class="flex items-start">
                            <i class="fas fa-magic text-pink-500 mr-3 mt-1"></i>
                            <div class="flex-1">
                                <div class="font-medium text-gray-800 mb-2">Special Instructions</div>
                                <div class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">${specialInstructions}</div>
                            </div>
                        </div>
                    </div>
                ` : ''}
            `;
        }

        // Enhanced form submission
        document.getElementById('request-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            // Final validation
            if (userCredits < 1) {
                alert('You need at least 1 credit to submit a request.');
                return;
            }

            if (!validateCurrentStep()) {
                return;
            }

            showLoading(true);

            try {
                const formData = new FormData(e.target);
                const requestData = {
                    songTitle: formData.get('songTitle'),
                    songType: formData.get('songType'),
                    description: formData.get('description'),
                    mood: formData.get('mood'),
                    duration: formData.get('duration'),
                    language: formData.get('language'),
                    specialInstructions: formData.get('specialInstructions'),
                    contactEmail: formData.get('contactEmail'),
                    userEmail: currentUser.email,
                    submittedAt: new Date().toISOString()
                };

                // Create the request with enhanced data
                const requestRef = await db.collection('requests').add({
                    userId: currentUser.uid,
                    ...requestData,
                    status: 'pending',
                    priority: 'normal',
                    estimatedCompletion: calculateEstimatedCompletion(),
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                });

                // Deduct credit from user with transaction
                await db.runTransaction(async (transaction) => {
                    const userRef = db.collection('users').doc(currentUser.uid);
                    const userDoc = await transaction.get(userRef);

                    if (!userDoc.exists) {
                        throw new Error('User document does not exist');
                    }

                    const currentCredits = userDoc.data().credits || 0;
                    if (currentCredits < 1) {
                        throw new Error('Insufficient credits');
                    }

                    transaction.update(userRef, {
                        credits: currentCredits - 1,
                        lastRequestAt: firebase.firestore.FieldValue.serverTimestamp()
                    });
                });

                showLoading(false);
                showSuccessModal();

                // Reset form and go back to step 1
                e.target.reset();
                currentStep = 1;
                updateStepVisibility();
                updateProgressBar();
                updateNavigationButtons();

                console.log('Request submitted successfully:', requestRef.id);

            } catch (error) {
                console.error('Error submitting request:', error);
                showLoading(false);

                if (error.message === 'Insufficient credits') {
                    alert('You don\'t have enough credits to submit this request. Please purchase more credits.');
                } else {
                    alert('There was an error submitting your request. Please try again.');
                }
            }
        });

        // Calculate estimated completion time
        function calculateEstimatedCompletion() {
            const now = new Date();
            const estimatedHours = 2; // Default 2 hours
            const completionTime = new Date(now.getTime() + (estimatedHours * 60 * 60 * 1000));
            return completionTime.toISOString();
        }

        // Buy credits function
        function buyCredits() {
            alert('Credit purchase feature coming soon! Contact support for immediate assistance.');
        }

        // Utility functions
        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        function showSuccessModal() {
            document.getElementById('success-modal').classList.remove('hidden');
        }

        function closeSuccessModal() {
            document.getElementById('success-modal').classList.add('hidden');
            loadUserData(); // Refresh credits
        }

        // Logout function
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await auth.signOut();
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
