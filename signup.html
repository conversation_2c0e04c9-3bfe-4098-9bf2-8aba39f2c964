<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Aifrobeats</title>
    <link rel="icon" href="images/mlogo.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    


    <style>
        /* Custom form validation styling */
        .form-field-error {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3) !important;
        }

        .form-field-success {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
        }

        /* Enhanced form animations */
        .signup-form-container {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Floating label effect */
        .form-group {
            position: relative;
        }

        .form-group input:focus + label,
        .form-group input:not(:placeholder-shown) + label {
            transform: translateY(-1.5rem) scale(0.8);
            color: #10b981;
        }

        .form-group label {
            position: absolute;
            left: 1rem;
            top: 1rem;
            transition: all 0.3s ease;
            pointer-events: none;
        }
    </style>
</head>
<body class="min-h-screen flex flex-col" style="background: linear-gradient(135deg, var(--hero-gradient-start), var(--hero-gradient-end));">

    <!-- Navigation Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white bg-opacity-10 backdrop-blur-md">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-2xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-white hover:text-green-400 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="login.html" class="secondary-button bg-white bg-opacity-10 text-white border-white">
                    <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                </a>
            </div>
        </nav>
    </header>

    <!-- Main Content Area -->
    <main class="flex-1 flex items-center justify-center py-20">
        <!-- Signup Container -->
        <div class="w-full max-w-lg mx-auto px-6">
        <div class="signup-form-container bg-white bg-opacity-15 backdrop-blur-xl rounded-3xl shadow-2xl border border-white border-opacity-30 overflow-hidden">

            <!-- Header with Enhanced Design -->
            <div class="text-center pt-8 pb-6 px-8">
                <div class="relative mb-6">
                    <div class="w-24 h-24 mx-auto bg-gradient-to-br from-green-400 via-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300">
                        <i class="fas fa-user-plus text-white text-3xl"></i>
                    </div>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center shadow-md">
                        <span class="text-yellow-800 text-xs font-bold">3</span>
                    </div>
                </div>
                <h1 class="text-4xl font-bold text-white mb-3 bg-gradient-to-r from-white to-green-200 bg-clip-text text-transparent">Join Aifrobeats!</h1>
                <p class="text-gray-200 text-lg">Create your account and get 3 free preview credits</p>
            </div>

            <!-- Enhanced Welcome Offer -->
            <div class="mx-8 mb-8">
                <div class="bg-gradient-to-r from-green-500 via-green-600 to-emerald-600 rounded-2xl p-6 text-center shadow-xl border border-green-400 border-opacity-30">
                    <div class="flex items-center justify-center mb-3">
                        <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-gift text-yellow-800 text-sm"></i>
                        </div>
                        <h3 class="text-white font-bold text-xl">Welcome Bonus!</h3>
                    </div>
                    <p class="text-green-100 text-base mb-4 font-medium">Get 3 FREE preview credits when you sign up</p>
                    <div class="grid grid-cols-2 gap-3 text-sm text-green-100">
                        <div class="flex items-center justify-center bg-white bg-opacity-20 rounded-lg py-2 px-3">
                            <i class="fas fa-check-circle mr-2 text-green-200"></i>
                            <span>No Credit Card</span>
                        </div>
                        <div class="flex items-center justify-center bg-white bg-opacity-20 rounded-lg py-2 px-3">
                            <i class="fas fa-bolt mr-2 text-green-200"></i>
                            <span>Instant Access</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Signup Form -->
            <form id="signup-form" class="px-8 pb-6 space-y-5">
                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-semibold text-gray-200 mb-2">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email" required
                                   class="w-full pl-10 pr-4 py-4 bg-white bg-opacity-15 border border-white border-opacity-40 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
                                   placeholder="Enter your email address">
                        </div>
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-semibold text-gray-200 mb-2">Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password" id="password" name="password" required
                                   class="w-full pl-10 pr-4 py-4 bg-white bg-opacity-15 border border-white border-opacity-40 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
                                   placeholder="Create a password (min 6 characters)">
                        </div>
                    </div>
                    <div>
                        <label for="confirm-password" class="block text-sm font-semibold text-gray-200 mb-2">Confirm Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-shield-alt text-gray-400"></i>
                            </div>
                            <input type="password" id="confirm-password" name="confirm-password" required
                                   class="w-full pl-10 pr-4 py-4 bg-white bg-opacity-15 border border-white border-opacity-40 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
                                   placeholder="Confirm your password">
                        </div>
                    </div>
                </div>
                <button type="submit" id="signup-btn" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                    <i id="signup-icon" class="fas fa-user-plus mr-3 text-lg"></i>
                    <span id="signup-text" class="text-lg">Create Account & Get 3 Free Credits</span>
                </button>
            </form>

            <!-- Error Message -->
            <div id="error-message" class="hidden mx-8 mb-4">
                <div class="bg-red-500 bg-opacity-20 border border-red-400 border-opacity-50 rounded-xl p-4 backdrop-blur-sm">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-300 mr-3"></i>
                        <p class="text-red-200 text-sm font-medium"></p>
                    </div>
                </div>
            </div>

            <!-- Success Message -->
            <div id="success-message" class="hidden mx-8 mb-4">
                <div class="bg-green-500 bg-opacity-20 border border-green-400 border-opacity-50 rounded-xl p-4 backdrop-blur-sm">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-300 mr-3"></i>
                        <p class="text-green-200 text-sm font-medium"></p>
                    </div>
                </div>
            </div>

            <!-- Enhanced Footer Links -->
            <div class="px-8 pb-8">
                <div class="space-y-4">
                    <!-- Sign In Link -->
                    <div class="text-center bg-white bg-opacity-10 rounded-xl p-4 backdrop-blur-sm">
                        <p class="text-gray-200 mb-2">Already have an account?</p>
                        <a href="login.html" class="inline-flex items-center text-green-300 hover:text-green-200 font-semibold text-lg transition-colors duration-300">
                            <i class="fas fa-sign-in-alt mr-2"></i>Sign in here
                        </a>
                    </div>

                    <!-- Terms -->
                    <div class="text-center bg-white bg-opacity-10 rounded-xl p-4 backdrop-blur-sm">
                        <p class="text-gray-300 text-sm mb-2">By creating an account, you agree to our</p>
                        <div class="flex justify-center space-x-4">
                            <a href="terms.html" class="text-green-300 hover:text-green-200 font-medium underline transition-colors duration-300">Terms of Service</a>
                            <span class="text-gray-400">•</span>
                            <a href="privacy.html" class="text-green-300 hover:text-green-200 font-medium underline transition-colors duration-300">Privacy Policy</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </main>

    <!-- Enhanced Footer -->
    <footer class="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-gray-300 py-12 mt-auto">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <!-- Logo and Brand -->
                <div class="flex items-center justify-center mb-6">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="w-8 h-8 mr-3">
                    <span class="animated-gradient-text text-2xl font-bold">Aifrobeats</span>
                </div>

                <!-- Tagline -->
                <p class="text-lg mb-6 text-gray-200">AI-Generated Afrobeats, Human-Curated for you.</p>

                <!-- Navigation Links -->
                <div class="flex justify-center space-x-8 mb-6 text-sm">
                    <a href="terms.html" class="hover:text-green-400 transition-colors duration-300 flex items-center">
                        <i class="fas fa-file-contract mr-2"></i>Terms of Service
                    </a>
                    <a href="privacy.html" class="hover:text-green-400 transition-colors duration-300 flex items-center">
                        <i class="fas fa-shield-alt mr-2"></i>Privacy Policy
                    </a>
                    <a href="index.html" class="hover:text-green-400 transition-colors duration-300 flex items-center">
                        <i class="fas fa-home mr-2"></i>Back to Home
                    </a>
                </div>

                <!-- Copyright -->
                <div class="border-t border-gray-700 pt-6">
                    <p class="text-sm text-gray-400">&copy; <span id="currentYear"></span> AIFROBEATS - All Rights Reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            <span class="text-gray-700">Creating your account...</span>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>


    <!-- Signup Script -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCRlkgzE2FMFFywISBm4GO4pfZ5pUFql0o",
            authDomain: "aifrobeats-com.firebaseapp.com",
            projectId: "aifrobeats-com",
            storageBucket: "aifrobeats-com.firebasestorage.app",
            messagingSenderId: "*************",
            appId: "1:*************:web:95074e13e4d40adaa164fa",
            measurementId: "G-YCQLRG8L44"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Custom signup workflow - NO Firebase UI
        let isSigningUp = false;

        // Custom signup form handler
        document.getElementById('signup-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            if (isSigningUp) {
                console.log('Already signing up, ignoring duplicate submission');
                return;
            }

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            console.log('Signup attempt:', { email, passwordLength: password.length, confirmPasswordLength: confirmPassword.length });

            // Validation
            if (!email || !password || !confirmPassword) {
                showError('Please fill in all fields.');
                return;
            }

            if (!email.includes('@') || !email.includes('.')) {
                showError('Please enter a valid email address.');
                return;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long.');
                return;
            }

            if (password !== confirmPassword) {
                showError('Passwords do not match.');
                return;
            }

            // Start signup process
            isSigningUp = true;
            setSignupButtonState('loading');
            showLoading(true);

            try {
                console.log('Step 1: Creating user with email and password...');

                // Step 1: Create user account
                const userCredential = await auth.createUserWithEmailAndPassword(email, password);
                const user = userCredential.user;

                console.log('Step 1 SUCCESS: User created:', user.uid, user.email);

                // Step 2: Create user document in Firestore
                console.log('Step 2: Creating user document in Firestore...');

                const userData = {
                    email: user.email,
                    credits: 3,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    requests: [],
                    profile: {
                        displayName: user.displayName || '',
                        photoURL: user.photoURL || '',
                        emailVerified: user.emailVerified
                    },
                    metadata: {
                        creationTime: user.metadata.creationTime,
                        lastSignInTime: user.metadata.lastSignInTime
                    }
                };

                await db.collection('users').doc(user.uid).set(userData);
                console.log('Step 2 SUCCESS: User document created');

                // Step 3: Verify the user is properly signed in
                console.log('Step 3: Verifying user authentication...');
                const currentUser = auth.currentUser;
                if (!currentUser || currentUser.uid !== user.uid) {
                    throw new Error('User authentication verification failed');
                }
                console.log('Step 3 SUCCESS: User authentication verified');

                // Step 4: Success - redirect to dashboard
                console.log('Step 4: Signup complete, redirecting...');
                showLoading(false);
                setSignupButtonState('success');
                showSuccess('Account created successfully! You have 3 free preview credits. Redirecting to dashboard...');

                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);

            } catch (error) {
                console.error('Signup error:', error);
                isSigningUp = false;
                showLoading(false);
                setSignupButtonState('error');

                // Handle specific errors
                if (error.code === 'auth/email-already-in-use') {
                    showError('This email is already registered. Please sign in instead or use a different email address.');
                } else if (error.code === 'auth/weak-password') {
                    showError('Password is too weak. Please use at least 6 characters with a mix of letters and numbers.');
                } else if (error.code === 'auth/invalid-email') {
                    showError('Please enter a valid email address.');
                } else if (error.code === 'auth/operation-not-allowed') {
                    showError('Email/password accounts are not enabled. Please contact support.');
                } else if (error.code === 'auth/network-request-failed') {
                    showError('Network error. Please check your internet connection and try again.');
                } else {
                    showError(`Signup failed: ${error.message || 'Please try again.'}`);
                }

                // Reset button after error
                setTimeout(() => {
                    setSignupButtonState('default');
                }, 3000);
            }
        });

        // Check if user is already signed in
        auth.onAuthStateChanged(async (user) => {
            if (user) {
                console.log('User already signed in:', user.uid);

                // Check if user document exists and create if missing
                try {
                    const userDoc = await db.collection('users').doc(user.uid).get();
                    if (!userDoc.exists) {
                        console.log('User document missing, creating...');
                        await db.collection('users').doc(user.uid).set({
                            email: user.email,
                            credits: 3,
                            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                            requests: [],
                            profile: {
                                displayName: user.displayName || '',
                                photoURL: user.photoURL || ''
                            }
                        });
                        console.log('User document created for existing user');
                    }
                } catch (error) {
                    console.error('Error checking/creating user document:', error);
                }

                // User is signed in, redirect to dashboard
                window.location.href = 'dashboard.html';
            } else {
                console.log('No user signed in, showing signup form');
            }
        });

        // Button state management
        function setSignupButtonState(state) {
            const button = document.getElementById('signup-btn');
            const icon = document.getElementById('signup-icon');
            const text = document.getElementById('signup-text');

            switch (state) {
                case 'loading':
                    button.disabled = true;
                    icon.className = 'fas fa-spinner fa-spin mr-3 text-lg';
                    text.textContent = 'Creating Your Account...';
                    break;
                case 'success':
                    button.disabled = true;
                    icon.className = 'fas fa-check mr-3 text-lg';
                    text.textContent = 'Account Created Successfully!';
                    break;
                case 'error':
                    button.disabled = true;
                    icon.className = 'fas fa-exclamation-triangle mr-3 text-lg';
                    text.textContent = 'Signup Failed - Try Again';
                    break;
                case 'default':
                default:
                    button.disabled = false;
                    icon.className = 'fas fa-user-plus mr-3 text-lg';
                    text.textContent = 'Create Account & Get 3 Free Credits';
                    break;
            }
        }

        // Utility functions
        function showError(message) {
            console.error('Error:', message);
            const errorDiv = document.getElementById('error-message');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');

            // Hide success message if showing
            document.getElementById('success-message').classList.add('hidden');

            setTimeout(() => errorDiv.classList.add('hidden'), 8000);
        }

        function showSuccess(message) {
            console.log('Success:', message);
            const successDiv = document.getElementById('success-message');
            successDiv.querySelector('p').textContent = message;
            successDiv.classList.remove('hidden');

            // Hide error message if showing
            document.getElementById('error-message').classList.add('hidden');

            setTimeout(() => successDiv.classList.add('hidden'), 8000);
        }

        function showLoading(show = true) {
            const overlay = document.getElementById('loading-overlay');
            if (show) {
                overlay.classList.remove('hidden');
            } else {
                overlay.classList.add('hidden');
            }
        }

        // Real-time form validation
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirm-password');

        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function validatePassword(password) {
            return password.length >= 6;
        }

        function validatePasswordMatch(password, confirmPassword) {
            return password === confirmPassword && password.length > 0;
        }

        function updateFieldValidation(input, isValid) {
            input.classList.remove('form-field-error', 'form-field-success');
            if (input.value.length > 0) {
                input.classList.add(isValid ? 'form-field-success' : 'form-field-error');
            }
        }

        // Email validation
        emailInput.addEventListener('input', () => {
            const isValid = validateEmail(emailInput.value);
            updateFieldValidation(emailInput, isValid);
        });

        // Password validation
        passwordInput.addEventListener('input', () => {
            const isValid = validatePassword(passwordInput.value);
            updateFieldValidation(passwordInput, isValid);

            // Also check confirm password if it has a value
            if (confirmPasswordInput.value.length > 0) {
                const passwordsMatch = validatePasswordMatch(passwordInput.value, confirmPasswordInput.value);
                updateFieldValidation(confirmPasswordInput, passwordsMatch);
            }
        });

        // Confirm password validation
        confirmPasswordInput.addEventListener('input', () => {
            const passwordsMatch = validatePasswordMatch(passwordInput.value, confirmPasswordInput.value);
            updateFieldValidation(confirmPasswordInput, passwordsMatch);
        });

        // Prevent form submission on Enter if validation fails
        document.getElementById('signup-form').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const email = emailInput.value.trim();
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (!validateEmail(email) || !validatePassword(password) || !validatePasswordMatch(password, confirmPassword)) {
                    e.preventDefault();
                    showError('Please fix the form errors before submitting.');
                }
            }
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
