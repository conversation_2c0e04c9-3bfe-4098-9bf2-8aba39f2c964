:root {
    /* Light theme (default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-card: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #4b5563;
    --text-muted: #6b7280;
    --border-color: rgba(0, 0, 0, 0.05);
    --shadow-color: rgba(0, 0, 0, 0.05);
    --shadow-color-hover: rgba(0, 0, 0, 0.1);
    --accent-color: #22c55e;
    --accent-color-dark: #16a34a;
    --header-bg: rgba(255, 255, 255, 0.95);
    --player-bg: rgba(255, 255, 255, 0.95);
    --hero-gradient-start: #111827;
    --hero-gradient-end: #1f2937;
    --footer-bg: #111827;
    --footer-text: #9ca3af;
    --card-hover-transform: translateY(-5px);

    /* Logo and title gradient colors */
    --logo-gradient-1: #22c55e; /* Green */
    --logo-gradient-2: #f97316; /* Orange */
    --logo-gradient-3: #8b5cf6; /* Purple */

    /* Timeline specific variables */
    --timeline-bg: linear-gradient(to bottom right, #1f2937, #22c55e);
    --timeline-line-bg: #d1d5db;
    --timeline-progress-bg: linear-gradient(to right, #22c55e, #16a34a);
    --timeline-step-active-bg: #22c55e;
    --timeline-step-inactive-bg: #6b7280;
    --timeline-text-active: #22c55e;
    --timeline-text-inactive: #9ca3af;
    --timeline-content-bg: rgba(0, 0, 0, 0.3);
    --timeline-bg-opacity: 0.7;
}

[data-theme="dark"] {
    /* Dark theme */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-card: #1f2937;
    --text-primary: #ffffff;
    --text-secondary: #f3f4f6;
    --text-muted: #d1d5db;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-color-hover: rgba(0, 0, 0, 0.4);
    --accent-color: #22c55e;
    --accent-color-dark: #16a34a;
    --header-bg: rgba(17, 24, 39, 0.95);
    --player-bg: rgba(17, 24, 39, 0.95);
    --hero-gradient-start: rgba(17, 24, 39, 0.85);
    --hero-gradient-end: rgba(31, 41, 55, 0.85);
    --footer-bg: #0f172a;
    --footer-text: #d1d5db;
    --card-hover-transform: translateY(-5px);

    /* Logo and title gradient colors - slightly brighter for dark mode */
    --logo-gradient-1: #4ade80; /* Brighter Green */
    --logo-gradient-2: #fb923c; /* Brighter Orange */
    --logo-gradient-3: #a78bfa; /* Brighter Purple */

    /* Timeline specific variables */
    --timeline-bg: linear-gradient(to bottom right, #111827, #22c55e);
    --timeline-line-bg: #4b5563;
    --timeline-progress-bg: linear-gradient(to right, #22c55e, #16a34a);
    --timeline-step-active-bg: #22c55e;
    --timeline-step-inactive-bg: #4b5563;
    --timeline-text-active: #22c55e;
    --timeline-text-inactive: #9ca3af;
    --timeline-content-bg: rgba(0, 0, 0, 0.3);
    --timeline-bg-opacity: 0.7;
}

body {
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
    padding-bottom: 80px; /* Add padding to prevent content from being hidden behind the player */
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.hero-bg {
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.85) 0%, rgba(31, 41, 55, 0.85) 100%);
    position: relative;
    overflow: hidden;
    min-height: 100vh; /* Make hero section at least full viewport height */
    display: flex;
    align-items: center;
    padding: 80px 0; /* Add padding to ensure content is visible on smaller screens */
}

.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
    z-index: 0;
}

/* Media query for mobile devices */
@media (max-width: 768px) {
    .hero-bg {
        min-height: 100vh; /* Ensure full height on mobile */
        padding: 60px 0; /* Adjust padding for mobile */
        background: linear-gradient(135deg, rgba(17, 24, 39, 0.9) 0%, rgba(31, 41, 55, 0.9) 100%), url('images/background.jpg');
        background-size: cover;
        background-position: center;
    }

    /* Hide video on mobile for better performance */
    .hero-video {
        display: none;
    }
}

.section-bg {
    background-color: var(--bg-secondary);
}

.primary-button {
    background: linear-gradient(45deg, var(--accent-color), var(--accent-color-dark));
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.7);
    animation: pulse 2s infinite;
    position: relative;
    overflow: hidden;
    z-index: 5;
}

.primary-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 15px 2px rgba(255, 165, 0, 0.9);
}

.primary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
}

.primary-button:hover::before {
    left: 100%;
}

.secondary-button {
    background-color: transparent;
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.secondary-button:hover {
    background-color: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.card {
    background-color: var(--bg-card);
    border-radius: 1rem;
    box-shadow: 0 10px 25px -5px var(--shadow-color), 0 8px 10px -6px var(--shadow-color);
    padding: 1.75rem;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.card:hover {
    transform: var(--card-hover-transform);
    box-shadow: 0 20px 25px -5px var(--shadow-color-hover), 0 10px 10px -5px var(--shadow-color-hover);
}

.feature-icon {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.25rem;
    transition: transform 0.3s ease;
}

@keyframes iconPulse {
    0% { transform: scale(1); }
    40% { transform: scale(1.5); } /* 2 seconds for expansion (40% of 5s) */
    100% { transform: scale(1); } /* 3 seconds for contraction (60% of 5s) */
}

.card:hover .feature-icon {
    animation: iconPulse 5s ease-in-out;
    animation-delay: 0.2s;
}

.audio-player {
    width: 100%;
    padding: 12px;
    background: linear-gradient(to right, #f3f4f6, #e5e7eb);
    border-radius: 12px;
    margin-top: 12px;
}

.audio-player audio {
    width: 100%;
}

.audio-player audio::-webkit-media-controls-panel {
    background: white;
}

.use-case-item {
    transition: all 0.3s ease;
}

.use-case-item:hover {
    transform: scale(1.05);
}

.faq-item {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.faq-item:hover {
    box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color);
}

.faq-header {
    cursor: pointer;
    padding: 1.25rem;
    background-color: var(--bg-card);
    color: var(--text-primary);
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.faq-content {
    padding: 0 1.25rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-content {
    padding: 1.25rem;
    max-height: 1000px;
}

.faq-icon {
    transition: transform 0.3s ease;
}

.faq-item.active .faq-icon {
    transform: rotate(180deg);
}

.nav-link {
    position: relative;
}

/* Enhanced Timeline Styles */
.production-timeline {
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.production-timeline:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.timeline-step {
    position: relative;
    z-index: 10;
    width: 25%;
    padding: 0 8px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    height: 120px;
}

.timeline-step div[role="tab"] {
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin: 0 auto;
}

.timeline-step.active div[role="tab"] {
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.3), 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 165, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 165, 0, 0);
    }
}

/* Ensure timeline text is visible in both themes */
.production-timeline h3,
.production-timeline p,
#timelineContent p {
    color: white !important;
}

/* Prevent layout shifts from timeline progress */
#timelineProgress {
    pointer-events: none;
    overflow: hidden; /* Ensure the handle doesn't overflow */
    contain: layout size paint; /* Contain the layout to prevent affecting parent elements */
}

#timelineProgress > div {
    width: 16px !important;
    height: 16px !important;
    right: 0 !important;
    contain: layout size paint; /* Contain the layout to prevent affecting parent elements */
}

/* Fix the timeline container to prevent layout shifts */
.production-timeline {
    contain: layout style paint; /* Contain all visual effects within this element */
    isolation: isolate; /* Create a new stacking context */
}

/* Timeline step styles for mobile */
@media (max-width: 640px) {
    .timeline-step {
        padding: 0 4px;
        height: 110px; /* Slightly reduced height for better proportions */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    /* Improved circle number styling */
    .timeline-step div[role="tab"] {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
        font-weight: 700;
        transition: all 0.3s ease;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
        border-radius: 50%;
        position: relative;
        top: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 5;
    }

    /* Active circle styling with enhanced visual effect */
    .timeline-step.active div[role="tab"] {
        width: 34px;
        height: 34px;
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3), 0 4px 6px rgba(0, 0, 0, 0.3);
        z-index: 10;
    }

    /* Consistent caption styling */
    .timeline-step h4 {
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0;
        width: 100%;
        text-align: center;
        line-height: 1.2;
    }

    /* Caption container with proper spacing */
    .timeline-step div.text-center.w-full.mb-4 {
        min-height: 2.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 3px !important; /* Reduced from 4px to 3px */
        padding: 0 2px;
    }

    /* Description text styling */
    .timeline-step p {
        font-size: 0.7rem;
        display: none;
        width: 100%;
        text-align: center;
        line-height: 1.2;
        margin-top: 2px;
    }

    .timeline-step.active p {
        display: block;
    }

    /* Timeline content styling */
    #timelineContent h4 {
        font-size: 1.1rem;
    }

    #timelineContent p {
        font-size: 0.85rem;
    }

    .production-timeline {
        padding: 1rem;
    }

    /* Consistent spacing between elements */
    .timeline-step > div:first-child {
        margin-bottom: 0 !important;
    }

    /* Proper spacing between circle and caption */
    .timeline-step div.text-center {
        margin-top: 2px;
        margin-bottom: 2px;
    }

    /* Consistent spacing for all steps */
    .timeline-step div.mb-4 {
        margin-bottom: 3px !important;
    }

    .timeline-step div.mt-4 {
        margin-top: 2px !important;
    }

    /* Ensure the timeline line is properly positioned */
    .absolute.h-2.top-1\/2.left-2.right-2.transform.-translate-y-1\/2.rounded-full,
    .absolute.h-2.top-1\/2.left-2.transform.-translate-y-1\/2.rounded-full {
        height: 3px;
    }
}

@media (max-width: 480px) {
    /* Smaller circle numbers for very small screens */
    .timeline-step div[role="tab"] {
        width: 26px;
        height: 26px;
        font-size: 0.8rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
        border-radius: 50%;
        position: relative;
        top: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* Active circle styling for very small screens */
    .timeline-step.active div[role="tab"] {
        width: 30px;
        height: 30px;
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3), 0 4px 6px rgba(0, 0, 0, 0.3);
    }

    /* Adjust caption font size */
    .timeline-step h4 {
        font-size: 0.7rem;
    }

    /* Reduce caption container height */
    .timeline-step div.text-center.w-full.mb-4 {
        min-height: 2rem;
    }

    /* Adjust timeline height */
    .timeline-step {
        height: 100px;
    }

    /* Ensure proper spacing for descriptions */
    .timeline-step p {
        font-size: 0.65rem;
    }
}

/* Site Logo and Title Styles */
.site-logo {
    width: 40px;
    height: 40px;
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.site-logo-sm {
    width: 32px;
    height: 32px;
    margin-right: 6px;
}

.site-logo-mobile {
    width: 36px;
    height: 36px;
    margin-right: 6px;
}

/* Logo bounce animation on hover */
.logo-container:hover .site-logo {
    animation: logoBounce 0.6s ease;
}

@keyframes logoBounce {
    0%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Animated gradient text for site title */
.animated-gradient-text {
    background-size: 300% 300%;
    background-image: linear-gradient(
        -45deg,
        var(--logo-gradient-1) 0%,
        var(--logo-gradient-2) 25%,
        var(--logo-gradient-3) 51%,
        var(--logo-gradient-1) 100%
    );
    animation: AnimateGradient 6s ease infinite;
    -webkit-background-clip: text;
    background-clip: text;
}

@keyframes AnimateGradient {
    0% { background-position: 0% 50% }
    50% { background-position: 100% 50% }
    100% { background-position: 0% 50% }
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #22c55e;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.sticky-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--header-bg);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 100;
    transition: all 0.3s ease;
    transform: translateY(-100%);
}

.sticky-nav.visible {
    transform: translateY(0);
}

/* Theme toggle button */
.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.theme-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-input {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

.form-input:focus {
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

/* Fix for select dropdowns in dark mode */
select.form-input option {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Dark mode specific form styles */
[data-theme="dark"] .form-input {
    border-color: #4b5563;
}

[data-theme="dark"] select.form-input option {
    background-color: #1f2937;
    color: #ffffff;
}

/* YouTube Play Button Style */
.youtube-play-btn {
    width: 68px;
    height: 48px;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.youtube-play-btn-inner {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.youtube-play-btn-triangle {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 12px 0 12px 20px;
    border-color: transparent transparent transparent var(--accent-color);
    margin-left: 5px;
}

.youtube-play-btn:hover .youtube-play-btn-inner {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.youtube-play-btn:hover {
    transform: scale(1.1);
}

/* Mobile responsive adjustments for YouTube play button */
@media (max-width: 576px) {
    .youtube-play-btn {
        width: 54px;
        height: 38px;
    }

    .youtube-play-btn-triangle {
        border-width: 10px 0 10px 16px;
    }
}

@media (max-width: 480px) {
    .youtube-play-btn {
        width: 48px;
        height: 34px;
    }

    .youtube-play-btn-triangle {
        border-width: 8px 0 8px 14px;
    }
}

/* Hamburger Menu Styles */
.hamburger-menu-button {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 20;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    transition: all 0.3s ease;
}

.hamburger-menu-button:hover {
    background-color: var(--bg-secondary);
}

.hamburger-icon {
    position: relative;
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
}

.hamburger-icon::before,
.hamburger-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
}

.hamburger-icon::before {
    transform: translateY(-6px);
}

.hamburger-icon::after {
    transform: translateY(6px);
}

.hamburger-menu-button.active .hamburger-icon {
    background-color: transparent;
}

.hamburger-menu-button.active .hamburger-icon::before {
    transform: rotate(45deg);
}

.hamburger-menu-button.active .hamburger-icon::after {
    transform: rotate(-45deg);
}

/* Smaller hamburger for sticky nav */
.hamburger-menu-button-sm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 20;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    transition: all 0.3s ease;
}

.hamburger-icon-sm {
    position: relative;
    width: 16px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
}

.hamburger-icon-sm::before,
.hamburger-icon-sm::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 2px;
    background-color: var(--text-primary);
    transition: all 0.3s ease;
}

.hamburger-icon-sm::before {
    transform: translateY(-5px);
}

.hamburger-icon-sm::after {
    transform: translateY(5px);
}

.hamburger-menu-button-sm.active .hamburger-icon-sm {
    background-color: transparent;
}

.hamburger-menu-button-sm.active .hamburger-icon-sm::before {
    transform: rotate(45deg);
}

.hamburger-menu-button-sm.active .hamburger-icon-sm::after {
    transform: rotate(-45deg);
}

/* Mobile menu container */
.mobile-menu-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.mobile-menu-container.open {
    max-height: 300px;
    transition: max-height 0.5s ease, padding 0.3s ease;
}

.social-icon {
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px);
    color: #22c55e;
}

.theme-text {
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.theme-text-secondary {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.theme-text-muted {
    color: var(--text-muted);
    transition: color 0.3s ease;
}

.theme-bg {
    background-color: var(--bg-card);
    transition: background-color 0.3s ease;
}

.wave-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
}

.wave-divider svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 70px;
}

.wave-divider .shape-fill {
    fill: #FFFFFF;
}

/* Footer styles */
.site-footer {
    background-color: #111827;
    color: #9ca3af;
}

.footer-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-col h3 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.footer-links a {
    display: flex;
    align-items: center;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: #4ade80;
}

.social-icon {
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #10b981;
    transform: translateY(-2px);
}

.newsletter-form input {
    background-color: #374151;
    border: none;
    color: #ffffff;
}

.newsletter-form button {
    background-color: #10b981;
    color: #ffffff;
    transition: background-color 0.3s ease;
}

.newsletter-form button:hover {
    background-color: #059669;
}

/* Custom Music Player Styles */
.music-player-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--player-bg);
    backdrop-filter: blur(10px);
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    height: 80px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease, background 0.3s ease;
    border-top: 1px solid var(--border-color);
}

.music-player-container.active {
    transform: translateY(0);
}

.music-player-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    transition: background 0.3s ease, transform 0.2s ease;
}

.music-player-toggle:hover {
    transform: scale(1.05);
}

.music-player-toggle i {
    font-size: 20px;
}

.music-player {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    gap: 15px;
}

.music-player-toggle {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: linear-gradient(to right, #22c55e, #16a34a);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    transition: all 0.3s ease;
}

.music-player-toggle:hover {
    transform: scale(1.05);
}

.music-player-toggle i {
    font-size: 1.2rem;
}

.music-player-album {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: visible;
    flex-shrink: 0;
    position: relative;
}

.music-player-album img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

/* Vinyl Record Styles */
.vinyl-container {
    position: relative;
}

.vinyl-record {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
    transform: scale(1.5);
    transform-origin: center;
    opacity: 0.85;
    transition: transform 0.5s ease;
    filter: drop-shadow(0 2px 4px var(--shadow-color));
    pointer-events: none; /* Allow clicks to pass through to the album art */
}

.vinyl-animation {
    animation: rotation 3s infinite linear;
}

@keyframes rotation {
    from {
        transform: scale(1.5) rotate(0deg);
    }
    to {
        transform: scale(1.5) rotate(359deg);
    }
}

.music-player-info {
    flex: 1;
    min-width: 0;
}

.music-player-title {
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.music-player-artist {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.3s ease;
}

.music-player-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.music-player-control {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.music-player-control:hover {
    color: #22c55e;
}

.music-player-control.play-pause {
    width: 40px;
    height: 40px;
    background: linear-gradient(to right, #22c55e, #16a34a);
    color: white;
    border-radius: 50%;
}

.music-player-progress-container {
    flex: 1;
    height: 4px;
    background-color: var(--bg-secondary);
    border-radius: 2px;
    cursor: pointer;
    margin: 0 15px;
    transition: background-color 0.3s ease;
}

.music-player-progress {
    height: 100%;
    background: linear-gradient(to right, #22c55e, #16a34a);
    border-radius: 2px;
    width: 0%;
    position: relative;
}

.music-player-progress-handle {
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: white;
    border: 2px solid #22c55e;
    border-radius: 50%;
    display: none;
}

.music-player-progress-container:hover .music-player-progress-handle {
    display: block;
}

.music-player-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    min-width: 80px;
    text-align: center;
    transition: color 0.3s ease;
}

.music-player-volume-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.music-player-volume-icon {
    color: var(--text-primary);
    cursor: pointer;
    transition: color 0.2s ease;
}

.music-player-volume-icon:hover {
    color: #22c55e;
}

.music-player-volume-slider {
    width: 80px;
    height: 4px;
    background-color: var(--bg-secondary);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.3s ease;
}

.music-player-volume-level {
    height: 100%;
    background: linear-gradient(to right, #22c55e, #16a34a);
    border-radius: 2px;
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .music-player-volume-container {
        display: none;
    }

    .music-player-time {
        display: none;
    }

    .music-player-controls {
        gap: 10px;
    }

    /* Fix container overflow issues */
    .container {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
        padding-left: 5px;
        padding-right: 5px;
    }

    /* Ensure all content fits within viewport */
    body {
        overflow-x: hidden;
        width: 100%;
    }

    /* Adjust sticky nav for mobile */
    .sticky-nav {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    /* Ensure cards don't overflow on mobile */
    .card {
        width: 100%;
        max-width: 100%;
    }

    /* Adjust grid layouts for mobile */
    .grid {
        margin-left: 0;
        margin-right: 0;
    }

    /* Style the mobile get song button */
    .mobile-get-song-btn {
        font-size: 12px !important;
        padding: 6px 10px !important;
        margin-left: 2px;
        /* Inherit all the animated button styles from primary-button */
        animation: pulse 2s infinite;
        position: relative;
        overflow: hidden;
        box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.7);
    }

    .mobile-get-song-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 0 15px 2px rgba(255, 165, 0, 0.9);
    }

    .mobile-get-song-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: all 0.6s ease;
    }

    .mobile-get-song-btn:hover::before {
        left: 100%;
    }

    /* Center all button text on mobile */
    .primary-button, .secondary-button, button {
        text-align: center;
        justify-content: center;
    }

    /* Footer styles for mobile */
    .site-footer {
        padding: 2rem 0;
    }

    .footer-container {
        padding: 5px !important;
    }

    .footer-col {
        padding: 5px;
        margin-bottom: 1rem;
    }

    .footer-links {
        margin-bottom: 1rem;
    }

    .newsletter-form {
        max-width: 100%;
    }

    /* Center all button text */
    button, .primary-button, .secondary-button {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .music-player-album {
        width: 40px;
        height: 40px;
    }

    .music-player-control {
        font-size: 1rem;
    }

    .music-player-control.play-pause {
        width: 36px;
        height: 36px;
    }

    /* Adjust vinyl record for mobile */
    .vinyl-record {
        transform: scale(1.4);
    }

    @keyframes rotation {
        from {
            transform: scale(1.4) rotate(0deg);
        }
        to {
            transform: scale(1.4) rotate(359deg);
        }
    }

    /* Further adjustments for smaller screens */
    h1 {
        font-size: 2rem !important;
        line-height: 1.2 !important;
    }

    h2 {
        font-size: 1.75rem !important;
    }

    .hero-bg {
        padding: 40px 0;
    }

    /* Ensure buttons don't overflow */
    .primary-button, .secondary-button {
        width: 100%;
        margin-bottom: 10px;
        justify-content: center;
    }

    /* Fix flex layouts on mobile */
    .flex-col-reverse {
        flex-direction: column-reverse !important;
    }

    /* Adjust text size for mobile */
    .animated-gradient-text {
        font-size: 1.5rem;
    }
}

/* Fixed background for Pidgin Chat section */
.fixed-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  z-index: -1;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: -1;
}
