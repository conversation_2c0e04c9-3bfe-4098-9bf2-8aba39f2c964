<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - Aifrobeats</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/png" href="favicon.png">
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <style>
        :root {
            --primary-color: #10b981;
            --primary-hover: #059669;
            --text-primary: #333333;
            --text-secondary: #4b5563;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --header-bg: rgba(255, 255, 255, 0.9);
            --footer-bg: #111827;
            --footer-text: #9ca3af;
            --border-color: #e5e7eb;
        }

        [data-theme="dark"] {
            --primary-color: #10b981;
            --primary-hover: #059669;
            --text-primary: #f9fafb;
            --text-secondary: #e5e7eb;
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --header-bg: rgba(17, 24, 39, 0.9);
            --footer-bg: #0f172a;
            --footer-text: #9ca3af;
            --border-color: #374151;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .theme-bg {
            background-color: var(--bg-primary);
        }

        .theme-text {
            color: var(--text-primary);
        }

        .primary-button {
            background-color: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .primary-button:hover {
            background-color: var(--primary-hover);
        }

        .theme-toggle {
            cursor: pointer;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        .theme-toggle:hover {
            color: var(--primary-color);
        }

        /* Hamburger Menu Styles */
        .hamburger-menu-button {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            cursor: pointer;
        }

        .hamburger-icon {
            width: 24px;
            height: 2px;
            background-color: currentColor;
            position: relative;
            transition: background-color 0.3s ease;
        }

        .hamburger-icon:before,
        .hamburger-icon:after {
            content: '';
            position: absolute;
            width: 24px;
            height: 2px;
            background-color: currentColor;
            transition: transform 0.3s ease;
        }

        .hamburger-icon:before {
            top: -8px;
        }

        .hamburger-icon:after {
            bottom: -8px;
        }

        .mobile-menu-container {
            transition: all 0.3s ease;
        }

        /* Sticky Navigation Styles */
        .sticky-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background-color: var(--header-bg);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 40;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .sticky-nav.visible {
            transform: translateY(0);
        }

        .mobile-get-song-btn {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
    </style>
</head>
<body>
    <header class="shadow-sm sticky top-0 z-50" style="background-color: var(--header-bg); transition: background-color 0.3s ease;">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <a href="index.html" class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-green-400 flex items-center">
                <i class="fas fa-music mr-2 text-green-500"></i>Aifrobeats
            </a>
            <div class="hidden md:flex space-x-6 items-center">
                <a href="index.html" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Home</a>
                <a href="index.html#how-it-works" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">How It Works</a>
                <a href="index.html#examples" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Our Music</a>
                <a href="music-library.html" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Music Library</a>
                <a href="index.html#use-cases" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Use Cases</a>
                <a href="index.html#pricing" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Pricing</a>
                <a href="index.html#faq" class="nav-link hover:text-green-600 transition-colors" style="color: var(--text-secondary);">FAQ</a>
                <a href="index.html#order-form-section" class="primary-button flex items-center">
                    <i class="fas fa-headphones-alt mr-2"></i>Get Your Custom Song
                </a>
                <div class="theme-toggle ml-4" id="themeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
            <div class="md:hidden flex items-center">
                <div class="theme-toggle mr-4" id="mobileThemeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
                <button id="mobile-menu-button" style="color: var(--text-primary);" class="focus:outline-none hamburger-menu-button">
                    <span class="hamburger-icon"></span>
                </button>
            </div>
        </nav>
        <div id="mobile-menu" class="hidden md:hidden px-6 pb-6 space-y-4 border-t theme-bg mobile-menu-container" style="border-color: var(--border-color); transition: background-color 0.3s ease;">
            <a href="index.html" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Home</a>
            <a href="index.html#how-it-works" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">How It Works</a>
            <a href="music-library.html" class="block py-3 hover:text-green-600 transition-colors theme-text text-lg font-medium">Music Library</a>
            <a href="index.html#order-form-section" class="primary-button w-full text-center flex items-center justify-center mt-4">
                <i class="fas fa-headphones-alt mr-2"></i>Get Your Custom Song
            </a>
        </div>
    </header>

    <div id="sticky-nav" class="sticky-nav">
        <div class="container mx-auto px-6 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.html" class="logo-container text-xl font-bold text-transparent flex items-center">
                    <img src="images/mlogo.png" alt="Aifrobeats Logo" class="site-logo-sm">
                    <span class="animated-gradient-text">Aifrobeats</span>
                </a>
            </div>
            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-4 items-center">
                <a href="index.html" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Home</a>
                <a href="index.html#how-it-works" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">How It Works</a>
                <a href="index.html#examples" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Our Music</a>
                <a href="music-library.html" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Music Library</a>
                <a href="index.html#pricing" class="text-sm hover:text-green-600 transition-colors" style="color: var(--text-secondary);">Pricing</a>
                <a href="index.html#order-form-section" class="primary-button text-sm py-2 px-4">Get Your Custom Song</a>
                <div class="theme-toggle ml-4" id="stickyThemeToggle" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </div>
            </div>
            <!-- Mobile Navigation -->
            <div class="md:hidden flex items-center justify-center ml-2">
                <a href="index.html#order-form-section" class="primary-button text-xs py-1 px-2 mobile-get-song-btn">Get Song</a>
            </div>
        </div>
    </div>

    <main>
        <section class="py-16 md:py-24">
            <div class="container mx-auto px-6 max-w-4xl">
                <div class="text-center mb-16" data-aos="fade-up">
                    <span class="inline-block bg-green-100 text-green-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">LEGAL</span>
                    <h1 class="text-3xl md:text-4xl font-bold mb-4 theme-text">Terms of Service</h1>
                    <p class="text-xl theme-text-muted max-w-2xl mx-auto">Last Updated: <span id="lastUpdated">June 15, 2023</span></p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up">
                    <h2 class="text-2xl font-bold mb-4 theme-text">1. Introduction</h2>
                    <p class="theme-text-muted mb-4">Welcome to Aifrobeats ("we," "our," or "us"). These Terms of Service ("Terms") govern your access to and use of the Aifrobeats website and services, including any content, functionality, and services offered on or through aifrobeats.com (the "Service").</p>
                    <p class="theme-text-muted mb-4">By accessing or using the Service, you agree to be bound by these Terms. If you do not agree to these Terms, you must not access or use the Service.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="text-2xl font-bold mb-4 theme-text">2. Service Description</h2>
                    <p class="theme-text-muted mb-4">Aifrobeats provides AI-generated, producer-curated Afrobeats music tracks customized according to user specifications. Our service creates unique music tracks based on the information you provide during the ordering process.</p>
                    <p class="theme-text-muted">We use artificial intelligence technology to generate multiple music options, which are then curated by our producers to select the best match for your requirements.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="200">
                    <h2 class="text-2xl font-bold mb-4 theme-text">3. Payment and Refund Policy</h2>
                    <p class="theme-text-muted mb-4"><strong>No Refund Policy:</strong> Due to the custom nature of our digital products, Aifrobeats operates under a strict no-refund policy. Once payment has been processed and our team begins work on your custom music track, no refunds will be issued.</p>
                    <p class="theme-text-muted mb-4">We encourage customers to provide detailed information about their music requirements to ensure satisfaction with the final product. Our producers will make reasonable efforts to select tracks that match your specifications.</p>
                    <p class="theme-text-muted">By placing an order with Aifrobeats, you acknowledge and agree to this no-refund policy.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="300">
                    <h2 class="text-2xl font-bold mb-4 theme-text">4. Intellectual Property and Licensing</h2>
                    <p class="theme-text-muted mb-4"><strong>Ownership of Rights:</strong> Upon completion of payment, you (the customer) own the full copyright to the music tracks delivered to you. This includes all song stems and components.</p>
                    <p class="theme-text-muted mb-4"><strong>Usage Rights:</strong> You receive full commercial and licensing rights to the music. You may:</p>
                    <ul class="list-disc pl-6 mb-4 theme-text-muted">
                        <li>Use the music for personal or commercial purposes</li>
                        <li>Modify, remix, or adapt the music</li>
                        <li>Use the music in videos, podcasts, advertisements, or any other media</li>
                        <li>Distribute the music as part of your own products or services</li>
                        <li>Monetize content containing the music</li>
                    </ul>
                    <p class="theme-text-muted">There are no restrictions on how you can use the music after purchase. The delivered music becomes your intellectual property.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="400">
                    <h2 class="text-2xl font-bold mb-4 theme-text">5. AI-Generated Content Disclaimer</h2>
                    <p class="theme-text-muted mb-4">The music provided by Aifrobeats is generated using artificial intelligence technology. While our producers curate and select the best AI-generated options, we provide the following disclaimers:</p>
                    <ul class="list-disc pl-6 mb-4 theme-text-muted">
                        <li>The AI may occasionally produce content that unintentionally resembles existing works, though our curation process aims to minimize this possibility.</li>
                        <li>The quality and style of AI-generated music may vary based on the specificity of your requirements and the capabilities of our current AI systems.</li>
                        <li>Our human producers make the final selection from multiple AI-generated options to ensure quality control.</li>
                    </ul>
                    <p class="theme-text-muted">By using our service, you acknowledge and accept the AI-generated nature of our music products.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="500">
                    <h2 class="text-2xl font-bold mb-4 theme-text">6. Delivery Timeframes</h2>
                    <p class="theme-text-muted mb-4">Aifrobeats strives to deliver completed music tracks within 1-3 hours of order confirmation and payment. However, delivery times may extend up to 6 hours in some cases due to:</p>
                    <ul class="list-disc pl-6 mb-4 theme-text-muted">
                        <li>High volume of orders</li>
                        <li>Complex curation requirements</li>
                        <li>Time zone differences</li>
                        <li>Technical factors related to AI generation</li>
                    </ul>
                    <p class="theme-text-muted mb-4">While we make every effort to meet our estimated delivery timeframes, these are not guaranteed. We appreciate your patience as our team works to provide you with the highest quality music possible.</p>
                    <p class="theme-text-muted">Aifrobeats cannot be held legally responsible for delays in delivery. We expect our clients to be patient for the best output.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="600">
                    <h2 class="text-2xl font-bold mb-4 theme-text">7. Dispute Resolution</h2>
                    <p class="theme-text-muted mb-4">If you have any concerns or disputes regarding our services, please contact us at <a href="mailto:<EMAIL>" class="text-green-600 hover:underline"><EMAIL></a>.</p>
                    <p class="theme-text-muted mb-4">We are committed to addressing your concerns in a fair and timely manner. Please include detailed information about your order and the specific nature of your dispute to help us resolve the matter efficiently.</p>
                    <p class="theme-text-muted">All disputes will be handled on a case-by-case basis according to these Terms of Service.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="700">
                    <h2 class="text-2xl font-bold mb-4 theme-text">8. Privacy Policy</h2>
                    <p class="theme-text-muted mb-4">We respect your privacy and are committed to protecting your personal information. Information collected during the ordering process is used solely for the purpose of fulfilling your order and improving our services.</p>
                    <p class="theme-text-muted mb-4">We do not sell or share your personal information with third parties except as necessary to provide our services (such as payment processing).</p>
                    <p class="theme-text-muted">For more details, please refer to our <a href="privacy.html" class="text-green-600 hover:underline">Privacy Policy</a>.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg mb-8" data-aos="fade-up" data-aos-delay="800">
                    <h2 class="text-2xl font-bold mb-4 theme-text">9. Modifications to Terms</h2>
                    <p class="theme-text-muted mb-4">Aifrobeats reserves the right to modify these Terms at any time. We will provide notice of significant changes by updating the "Last Updated" date at the top of these Terms.</p>
                    <p class="theme-text-muted">Your continued use of the Service after any changes indicates your acceptance of the modified Terms.</p>
                </div>

                <div class="theme-bg p-8 rounded-xl shadow-lg" data-aos="fade-up" data-aos-delay="900">
                    <h2 class="text-2xl font-bold mb-4 theme-text">10. Contact Information</h2>
                    <p class="theme-text-muted mb-4">If you have any questions about these Terms, please contact us at:</p>
                    <p class="theme-text-muted mb-2"><i class="fas fa-envelope text-green-500 mr-2"></i> <a href="mailto:<EMAIL>" class="hover:text-green-600 transition-colors"><EMAIL></a></p>
                    <p class="theme-text-muted"><i class="fas fa-phone-alt text-green-500 mr-2"></i> <a href="tel:+2347038808350" class="hover:text-green-600 transition-colors">+234 ************</a></p>
                </div>
            </div>
        </section>
    </main>

<section id="pidgin-chat" class="py-16 md:py-24 relative overflow-hidden">
  <!-- Fixed background image -->
  <div class="fixed-bg absolute inset-0 z-0" style="background-image: url('images/background.jpg');"></div>
  <div class="overlay absolute inset-0 bg-black bg-opacity-60 z-0"></div>
  
  <div class="container mx-auto px-6 text-center relative z-10">
    <h2 class="text-3xl md:text-4xl font-bold mb-6 text-white">Create Your Afrobeat Lyrics with Pidgin Chat</h2>
    <div class="max-w-xs mx-auto">
      <a href="https://aifrobeats.com/pidginchat" class="primary-button text-lg flex items-center justify-center">
        <i class="fas fa-paper-plane mr-2"></i> Start Chatting
      </a>
    </div>
  </div>
</section>

    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Aifrobeats Info -->
                <div>
                    <div class="flex items-center text-green-500 mb-4">
                        <i class="fas fa-music mr-2"></i>
                        <h3 class="text-xl font-bold">Aifrobeats</h3>
                    </div>
                    <p class="text-gray-400 mb-4">AI-Generated Afrobeats, Human-Curated for you. Fast, affordable, and unique music for every occasion.</p>
                    <div class="flex items-center mb-2">
                        <i class="fas fa-envelope text-green-500 mr-2"></i>
                        <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-green-400 transition-colors"><EMAIL></a>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone-alt text-green-500 mr-2"></i>
                        <a href="tel:+2347038808350" class="text-gray-400 hover:text-green-400 transition-colors">+234 ************</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="index.html" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Home
                            </a>
                        </li>
                        <li>
                            <a href="index.html#how-it-works" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>How It Works
                            </a>
                        </li>
                        <li>
                            <a href="music-library.html" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Music Library
                            </a>
                        </li>
                        <li>
                            <a href="index.html#pricing" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Pricing
                            </a>
                        </li>
                        <li>
                            <a href="index.html#faq" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>FAQ
                            </a>
                        </li>
                        <li>
                            <a href="index.html#order-form-section" class="text-gray-400 hover:text-green-400 transition-colors flex items-center">
                                <i class="fas fa-chevron-right text-xs text-green-500 mr-2"></i>Order Now
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Connect With Us -->
                <div>
                    <h3 class="text-xl font-bold mb-4">Connect With Us</h3>
                    <p class="text-gray-400 mb-4">Follow us for updates and examples!</p>
                    <div class="flex space-x-3 mb-5">
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-9 h-9 bg-gray-800 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all">
                            <i class="fab fa-soundcloud"></i>
                        </a>
                    </div>
                    <div class="bg-gray-800 p-3 rounded-lg">
                        <h4 class="text-white text-sm font-semibold mb-2">Subscribe to our newsletter</h4>
                        <form class="flex">
                            <input type="email" placeholder="Your email" class="bg-gray-700 border-0 text-white text-sm rounded-l-lg focus:ring-green-500 focus:border-green-500 flex-grow p-2">
                            <button type="submit" class="bg-green-600 text-white px-3 rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-8 pt-6 border-t border-gray-800 text-center text-sm">
                <p class="text-gray-400">&copy; <span id="currentYear"></span> AIFROBEATS CUSTOM MUSIC PRODUCTIONS – All Rights Reserved.</p>
                <div class="mt-2 flex flex-wrap justify-center gap-4">
                    <a href="terms.html" class="text-gray-400 hover:text-green-400 transition-colors">Terms of Service</a>
                    <span class="hidden md:inline text-gray-600">|</span>
                    <a href="privacy.html" class="text-gray-400 hover:text-green-400 transition-colors">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            once: true
        });

        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const mobileThemeToggle = document.getElementById('mobileThemeToggle');
            const stickyThemeToggle = document.getElementById('stickyThemeToggle');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const stickyNav = document.getElementById('sticky-nav');

            // Check for saved theme preference or use device preference
            const savedTheme = localStorage.getItem('theme') ||
                (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

            // Apply the saved theme
            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }

            // Theme toggle event
            themeToggle.addEventListener('click', function() {
                toggleTheme();
            });

            // Mobile theme toggle event
            if (mobileThemeToggle) {
                mobileThemeToggle.addEventListener('click', function() {
                    toggleTheme();
                });
            }

            // Sticky theme toggle event
            if (stickyThemeToggle) {
                stickyThemeToggle.addEventListener('click', function() {
                    toggleTheme();
                });
            }

            function toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                if (newTheme === 'dark') {
                    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                } else {
                    themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    if (mobileThemeToggle) mobileThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    if (stickyThemeToggle) stickyThemeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                }
            }

            // Mobile menu toggle
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Sticky navigation
            if (stickyNav) {
                let lastScrollTop = 0;
                const headerHeight = document.querySelector('header').offsetHeight;

                window.addEventListener('scroll', function() {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    // Show sticky nav when scrolling down past the header
                    if (scrollTop > headerHeight && scrollTop > lastScrollTop) {
                        stickyNav.classList.add('visible');
                    }
                    // Hide sticky nav when scrolling up to the top
                    else if (scrollTop < lastScrollTop || scrollTop <= headerHeight) {
                        stickyNav.classList.remove('visible');
                    }

                    lastScrollTop = scrollTop;
                });
            }
        });
    </script>
</body>
</html>
